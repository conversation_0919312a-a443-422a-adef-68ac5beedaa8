<template>
    <div>
        <a-card :bordered="false">
            <div class="table-page-search-wrapper">
                <a-form layout="inline">
                    <a-row :gutter="48">
                        <a-col :md="12" :lg="6" :sm="24">
                            <a-form-item label="用户手机">
                                <a-input v-model="queryParam.phone" placeholder="请输入用户手机" />
                            </a-form-item>
                        </a-col>
                        <a-col :md="12" :lg="6" :sm="24">
                            <a-form-item label="用户姓名">
                                <a-input v-model="queryParam.realName" placeholder="请输入用户姓名" />
                            </a-form-item>
                        </a-col>
                        <a-col :md="12" :lg="6" :sm="24">
                            <a-form-item label="持仓订单号">
                                <a-input v-model="queryParam.positionSn" style="width: 100%" placeholder="请输入持仓订单号" />
                            </a-form-item>
                        </a-col>
                    </a-row>
                    <a-row :gutter="48">
                        <a-col :md="12" :lg="6" :sm="24">
                            <a-form-item>
                                <span class="table-page-search-submitButtons">
                                    <a-button @click="getQueryParam" icon="redo">
                                        重置</a-button>
                                    <a-button type="primary" icon="search" style="margin-left: 8px"
                                        @click="queryParam.pageNum = 1, getList()">查询
                                    </a-button>

                                </span>
                            </a-form-item>
                        </a-col>
                    </a-row>
                </a-form>
            </div>
        </a-card>
        <a-table bordered :loading="loading" :pagination="pagination" :columns="columns" :data-source="datalist"
            rowKey="id" :scroll="{ x: 1440 }">

            <span slot="stockName" slot-scope="text,record">
                <template>
                    <div>
                        <span style="margin-right:10px">{{ record.stockName }}</span>
                        <a-tag
                            :color="record.stockPlate == '科创' ? 'blue' : !record.stockPlate ? 'orange' : record.stockPlate == '创业' ? 'pink' : 'purple'">
                            {{ record.stockPlate == '科创' ? '科创' : !record.stockPlate ? '股票' : record.stockPlate }}
                        </a-tag>
                        <p>({{ record.stockCode }})</p>
                    </div>
                </template>
            </span>
            <span slot="orderDirection" slot-scope="text,record">
                <template>
                    <div>
                        <a-tag :color="record.orderDirection == '买涨' ? 'red' : 'green'">
                            {{ record.orderDirection }}
                        </a-tag>
                    </div>
                </template>
            </span>
            <span slot="now_price" slot-scope="text,record">
                <template>
                    <div>
                        <p
                            :class="Number(record.now_price) - record.buyOrderPrice < 0 ? 'greens' : Number(record.now_price) - record.buyOrderPrice > 0 ? 'reds' : ''">
                            {{ record.now_price }}
                        </p>
                    </div>
                </template>
            </span>
            <span slot="profitAndLose" slot-scope="text">
                <template>
                    <div>
                        <p :class="text < 0 ? 'greens' : text > 0 ? 'reds' : ''">
                            {{ text }}
                        </p>
                    </div>
                </template>
            </span>
            <span slot="allProfitAndLose" slot-scope="text">
                <template>
                    <div>
                        <p :class="text < 0 ? 'greens' : text > 0 ? 'reds' : ''">
                            {{ text }}
                        </p>
                    </div>
                </template>
            </span>

            <template slot="action" slot-scope="text,record">
                <a slot="action" href="javascript:;" @click="LockVisibleDialog = true; clickPositionId = record.id"
                    v-if="record.isLock == 0">锁仓</a>
                <a slot="action" href="javascript:;" @click="getLockOpen(record.id)" v-else>解锁</a>
                <a-divider type="vertical" />
                <a slot="action" href="javascript:;" @click="getCompulsoryClosing(record.positionSn)">强制平仓</a>
            </template>
        </a-table>
        <a-modal title="锁仓" :width="640" :visible="LockVisibleDialog" :confirmLoading="LockVisibleLoading"
                 @ok="getDialogOk" @cancel="handleCancel">
            <a-form :form="LockVisibleForm" ref="createModal">
                <a-form-item>
                    <a-input
                        v-decorator="['lockMsg', { rules: [{ required: true, message: '请输入锁仓原因！', whitespace: true }] }]"
                        placeholder="请输入锁仓原因！" />
                </a-form-item>
            </a-form>
        </a-modal>
    </div>
</template>
<script>
import { positionList, positionLock, positionSell } from '@/api/position'
import moment from 'moment'
export default {
    name: 'financinghold',
    data() {
        return {
            columns: [
                {
                    title: '融资名称',
                    dataIndex: 'stockName',
                    align: 'center',
                    width: 120,
                    scopedSlots: { customRender: 'stockName' },
                },
                {
                    title: '用户名称（ID）',
                    dataIndex: 'nickName',
                    align: 'center',
                    width: 140,
                    customRender: (text, row, index) => {
                        return `${row.nickName}（${row.userId}）`
                    }
                },
                {
                    title: '持仓订单号（ID）',
                    dataIndex: 'positionSn',
                    align: 'center',
                    width: 180,
                    customRender: (text, row, index) => {
                        return `${row.positionSn}（${row.id}）`
                    }
                },
                {
                    title: '买卖方向',
                    dataIndex: 'orderDirection',
                    align: 'center',
                    width: 80,
                    scopedSlots: { customRender: 'orderDirection' },
                },
                {
                    title: '买入价',
                    dataIndex: 'buyOrderPrice',
                    align: 'center',
                    width: 90,
                    customRender: (text, row, index) => {
                        return text.toFixed(2)
                    }
                },
                {
                    title: '现价',
                    dataIndex: 'now_price',
                    align: 'center',
                    width: 90,
                    scopedSlots: { customRender: 'now_price' },
                },
                {
                    title: '浮动盈亏',
                    dataIndex: 'profitAndLose',
                    align: 'center',
                    width: 100,
                    scopedSlots: { customRender: 'profitAndLose' },
                },
                {
                    title: '总盈亏',
                    dataIndex: 'allProfitAndLose',
                    align: 'center',
                    width: 100,
                    scopedSlots: { customRender: 'allProfitAndLose' },
                },
                {
                    title: '数量（股）',
                    dataIndex: 'orderNum',
                    align: 'center',
                    width: 90,
                },
                {
                    title: '总市值',
                    dataIndex: 'orderTotalPrice',
                    align: 'center',
                    width: 100,
                },
                {
                    title: '手续费',
                    dataIndex: 'orderFee',
                    align: 'center',
                    width: 90,
                },
                {
                    title: '买入时间',
                    dataIndex: 'buyOrderTime',
                    align: 'center',
                    width: 140,
                    customRender: (text, row, index) => {
                        return text ? moment(text).format('YYYY-MM-DD HH:mm:ss') : ''
                    }
                },
                {
                    title: '操作',
                    key: 'action',
                    align: 'center',
                    fixed: 'right',
                    width: 120,
                    scopedSlots: { customRender: 'action' },
                },
            ],
            //表头
            pagination: {
                total: 0,
                pageSize: 10,//每页中显示10条数据
                showSizeChanger: true,
                pageSizeOptions: ["10", "20", "50", "100"],//每页中显示的数据
                onShowSizeChange: (current, pageSize) => this.onSizeChange(current, pageSize), // 改变每页数量时更新显示
                onChange: (page, pageSize) => this.onPageChange(page, pageSize),//点击页码事件
                showTotal: total => `共有 ${total} 条数据`,  //分页中显示总的数据
            },
            loading: false,
            queryParam: {
                pageNum: 1,
                pageSize: 10,
                agentId: undefined,
                userId: '',
                positionSn: '',
                state: 0,
            },
            datalist: [],
            agentList: [],
            agentLoading: false,
            LockVisibleDialog: false,
            LockVisibleLoading: false,
            LockVisibleForm: this.$form.createForm(this),
            clickPositionId: '',
            agentQueryParam: {
                pageNum: 1,
                pageSize: 100,
            },
        }
    },
    created() {
        this.getList()
    },
    methods: {
        getCompulsoryClosing(val) {
            var that = this
            this.$confirm({
                title: '提示',
                content: '确认要强制平仓吗?',
                onOk() {
                    var data = {
                        positionSn: val,
                    }
                    positionSell(data).then(res => {
                        if (res.status == 0) {
                            that.$message.success({ content: res.msg, duration: 2 });
                            that.getList()
                        } else {
                            that.$message.error({ content:  res.msg});
                        }
                    }).catch(error => {
                        that.$message.error({ content: '平仓失败' });
                    })
                },
                onCancel() {
                    console.log('Cancel');
                },
            });
        },
        getLockOpen(val) {
            var that = this
            this.$confirm({
                title: '提示',
                content: '确认要解锁该持仓单?',
                onOk() {
                    var data = {
                        state: 0,
                        positionId: val,
                    }
                    positionLock(data).then(res => {
                        if (res.status == 0) {
                            that.$message.success({ content: res.msg, duration: 2 });
                            that.getList()
                        } else {
                            that.$message.error({ content: res.msg });
                        }
                    })
                },
                onCancel() {
                    console.log('Cancel');
                },
            });
        },
        handleCancel() {
            this.LockVisibleDialog = false
            const form = this.$refs.createModal.form
            form.resetFields()
        },
        getDialogOk() {
            const form = this.$refs.createModal.form
            form.validateFields((errors, values) => {
                if (!errors) {
                    this.LockVisibleLoading = true
                    var data = {
                        state: 1,
                        lockMsg: values.lockMsg,
                        positionId: this.clickPositionId,
                    }
                    positionLock(data).then(res => {
                        if (res.status == 0) {
                            this.LockVisibleDialog = false
                            this.$message.success({ content: res.msg, duration: 2 });
                            form.resetFields()
                            this.getList()
                        } else {
                            this.$message.error({ content: res.msg });
                        }
                        this.LockVisibleLoading = false
                    }).catch(error => {
                        reject(error)
                    })
                }
            })
        },
        getInit(state = 0) {
            this.queryParam = {
                pageNum: 1,
                pageSize: 10,
                agentId: undefined,
                userId: '',
                positionSn: '',
                state: state,
            }
            this.getList()
        },
        getQueryParam() {
            this.queryParam = {
                pageNum: 1,
                pageSize: 10,
                agentId: undefined,
                userId: '',
                positionSn: '',
                state: 0,
            }
        },

        getList() {
            this.loading = true
            positionList(this.queryParam).then(res => {
                this.datalist = res.data.list
                this.pagination.total = res.data.total
                this.loading = false
            })
        },
        onPageChange(page, pageSize) {
            this.queryParam.pageNum = page
            this.getList()
        },
        onSizeChange(current, pageSize) {
            this.queryParam.pageNum = current
            this.queryParam.pageSize = pageSize
            this.getList()
        },
    }
}
</script>
<style scoped>
.greens {
    color: #52c41a;
}

.reds {
    color: #f5222d;
}
</style>