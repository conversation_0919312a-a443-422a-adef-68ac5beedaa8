import request from '@/utils/request'
import qs from 'qs'
const userApi = {
    positionlist: '/agent/position/list.do', // 融资列表
    indexpositionlist: '/agent/index/position/list.do', // 指数列表
    futurespositionlist: '/agent/futures/position/list.do', // 期货列表
    positionList: '/agent/position/list.do', // 融资列表
    positionApprove: '/agent/position/approve.do', // 审批
    batchApprove: '/agent/position/batchApprove.do', // 审批
    positionLock: '/agent/position/lock.do', // 融资锁仓 解锁
    positionSell: '/agent/position/sell.do', // 融资强制平仓
    positionDel: '/agent/position/del.do', //  融资平仓删除
    userDetail: '/agent/user/detail.do', // 创建融资持仓 单个用户详情搜索
    stockGetSingleStock: '/agent/stock/getSingleStock.do', // 创建融资持仓 获取单个股票信息
    positionCreate: '/agent/position/create.do', // 创建融资持仓
}

export function positionlist(parameter) {
    return request({
        url: userApi.positionlist,
        method: 'post',
        data: qs.stringify(parameter),
    })
}
export function indexpositionlist(parameter) {
    return request({
        url: userApi.indexpositionlist,
        method: 'post',
        data: qs.stringify(parameter),
    })
}
export function futurespositionlist(parameter) {
    return request({
        url: userApi.futurespositionlist,
        method: 'post',
        data: qs.stringify(parameter),
    })
}


export function positionList(parameter) {
    return request({
        url: userApi.positionList,
        method: 'post',
        data: qs.stringify(parameter),
    })
}

export function positionLock(parameter) {
    return request({
        url: userApi.positionLock,
        method: 'post',
        data: qs.stringify(parameter),
    })
}

export function positionSell(parameter) {
    return request({
        url: userApi.positionSell,
        method: 'post',
        data: qs.stringify(parameter),
    })
}

export function positionDel(parameter) {
    return request({
        url: userApi.positionDel,
        method: 'post',
        data: qs.stringify(parameter),
    })
}

export function userDetail(parameter) {
    return request({
        url: userApi.userDetail,
        method: 'post',
        data: qs.stringify(parameter),
    })
}

export function stockGetSingleStock(parameter) {
    return request({
        url: userApi.stockGetSingleStock,
        method: 'post',
        data: qs.stringify(parameter),
    })
}

export function positionCreate(parameter) {
    return request({
        url: userApi.positionCreate,
        method: 'post',
        data: qs.stringify(parameter),
    })
}
