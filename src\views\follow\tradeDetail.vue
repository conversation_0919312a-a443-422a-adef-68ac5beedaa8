<template>
  <div class="trade-detail-list">
    <a-card :bordered="false">
      <!-- 搜索区域 -->
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="24">
            <a-col :md="6" :sm="24">
              <a-form-item label="用户信息">
                <a-input v-model="queryParams.keyword" placeholder="用户姓名、手机号、账号" allowClear />
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <a-form-item label="状态">
                <a-select v-model="queryParams.status" placeholder="状态" allowClear style="width: 160px">
                  <a-select-option :value="''">所有状态</a-select-option>
                  <a-select-option :value="1">跟单中</a-select-option>
                  <a-select-option :value="2">已清仓</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <a-form-item label="结算状态">
                <a-select v-model="queryParams.settleStatus" placeholder="结算状态" allowClear style="width: 160px">
                  <a-select-option :value="''">所有状态</a-select-option>
                  <a-select-option :value="0">未结算</a-select-option>
                  <a-select-option :value="1">已结算</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <a-form-item>
                <a-button type="primary" @click="fetchList">查询</a-button>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </div>

      <!-- 表格区域 -->
      <a-table :columns="columns" :dataSource="tableData.list" :pagination="pagination" rowKey="id" bordered
        :scroll="{ x: 2500 }" @change="handleTableChange" :loading="loading">
        <!-- 套餐类型 -->
        <template slot="packageType" slot-scope="text">
          <a-tag v-if="text === 1" color="orange">普通单单</a-tag>
          <a-tag v-else-if="text === 2" color="blue">套餐产品</a-tag>
          <a-tag v-else color="gray">-</a-tag>
        </template>

        <!-- 结算状态 -->
        <template slot="settlementStatus" slot-scope="text">
          <span v-if="text === 0">未结算</span>
          <span v-else-if="text === 1" style="color: #1890ff;">已结算</span>
          <span v-else style="color: gray;">-</span>
        </template>

        <!-- 状态 -->
        <template slot="status" slot-scope="text">
          <span v-if="text === 1">跟单中</span>
          <span v-else-if="text === 2" style="color: #f5222d;">已清仓</span>
          <span v-else style="color: gray;">-</span>
        </template>

        <!-- 操作 -->
        <span slot="action" slot-scope="text, record">
          <a-space>
            <a @click="showDetail(record)">详情</a>
          </a-space>
        </span>
      </a-table>

      <!-- 详情弹窗 -->
      <a-modal title="跟单详情" v-model="detailDialogVisible" :width="800" :footer="null">
        <a-descriptions :column="2" bordered v-if="detailData">
          <a-descriptions-item label="跟单ID">{{ detailData.id }}</a-descriptions-item>
          <a-descriptions-item label="跟单单号">{{ detailData.followNo }}</a-descriptions-item>

          <a-descriptions-item label="会员账号">{{ detailData.userInfo?.userAccount }}</a-descriptions-item>
          <a-descriptions-item label="会员姓名">{{ detailData.userInfo?.userName }}</a-descriptions-item>
          <a-descriptions-item label="会员手机">{{ detailData.userInfo?.userPhone }}</a-descriptions-item>
          <a-descriptions-item label="跟单金额">{{ detailData.userInfo?.amount }}</a-descriptions-item>

          <a-descriptions-item label="导师账号">{{ detailData.mentorInfo?.mentorAccount }}</a-descriptions-item>
          <a-descriptions-item label="导师姓名">{{ detailData.mentorInfo?.mentorName }}</a-descriptions-item>
          <a-descriptions-item label="导师手机">{{ detailData.mentorInfo?.mentorPhone }}</a-descriptions-item>
          <a-descriptions-item label="佣金比例">
            {{ detailData.mentorInfo?.salaryRate ? detailData.mentorInfo.salaryRate + '%' : '-' }}
          </a-descriptions-item>

          <a-descriptions-item label="股票信息" :span="2">
            <a-descriptions bordered :column="2" size="small">
              <a-descriptions-item label="股票代码">{{ detailData.stockInfo?.stockCode }}</a-descriptions-item>
              <a-descriptions-item label="股票名称">{{ detailData.stockInfo?.stockName }}</a-descriptions-item>
              <a-descriptions-item label="买入数量">{{ detailData.stockInfo?.buyQuantity }}</a-descriptions-item>
              <a-descriptions-item label="现有数量">{{ detailData.stockInfo?.currentQuantity }}</a-descriptions-item>
              <a-descriptions-item label="卖出数量">{{ detailData.stockInfo?.sellQuantity }}</a-descriptions-item>
              <a-descriptions-item label="买入单价">{{ detailData.stockInfo?.buyPrice }}</a-descriptions-item>
              <a-descriptions-item label="卖出单价">{{ detailData.stockInfo?.sellPrice }}</a-descriptions-item>
              <a-descriptions-item label="投顾收益">{{ detailData.stockInfo?.advisorProfit }}</a-descriptions-item>
              <a-descriptions-item label="投顾收益比例">
                {{ detailData.stockInfo?.advisorProfitRate ? (detailData.stockInfo.advisorProfitRate * 100).toFixed(2) +
                  '%' :
                  '-' }}
              </a-descriptions-item>
            </a-descriptions>
          </a-descriptions-item>

          <a-descriptions-item label="建仓时间">{{ detailData.stockInfo?.buyTime || '-' }}</a-descriptions-item>
          <a-descriptions-item label="平仓时间">{{ detailData.stockInfo?.sellTime || '-' }}</a-descriptions-item>
          <a-descriptions-item label="结算时间">{{ detailData.settlementTime || '-' }}</a-descriptions-item>
          <a-descriptions-item label="结算状态">
            <template v-if="detailData.settlementStatus === 0">未结算</template>
            <template v-else-if="detailData.settlementStatus === 1">
              <span style="color: #1890ff;">已结算</span>
            </template>
            <template v-else>-</template>
          </a-descriptions-item>

          <a-descriptions-item label="套餐类型">
            <a-tag v-if="detailData.packageType === 1" color="orange">普通单单</a-tag>
            <a-tag v-else-if="detailData.packageType === 2" color="blue">套餐产品</a-tag>
            <a-tag v-else color="gray">-</a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="状态">
            <template v-if="detailData.status === 1">跟单中</template>
            <template v-else-if="detailData.status === 2">
              <span style="color: #f5222d;">已清仓</span>
            </template>
            <template v-else>-</template>
          </a-descriptions-item>
        </a-descriptions>
      </a-modal>

      <!-- 编辑弹窗 -->
      <a-modal title="修改" v-model="editDialogVisible" v-if="editDialogVisible && editForm.stockInfo" @ok="submitEdit"
        @cancel="() => editDialogVisible = false">
        <a-form :model="editForm" :labelCol="{ span: 6 }" :wrapperCol="{ span: 14 }">
          <a-form-item label="买入价格">
            <a-input v-model="editForm.stockInfo.buyPrice" />
          </a-form-item>
          <a-form-item label="卖出价格">
            <a-input v-model="editForm.stockInfo.sellPrice" />
          </a-form-item>
          <a-form-item label="买入时间">
            <a-input v-model="editForm.stockInfo.buyTime" />
          </a-form-item>
          <a-form-item label="卖出时间">
            <a-input v-model="editForm.stockInfo.sellTime" />
          </a-form-item>
        </a-form>
      </a-modal>
    </a-card>
  </div>
</template>

<script>
import { pageList, getDetail, update } from '@/api/tradeDetail/tradeDetail'

export default {
  data() {
    return {
      loading: false, // 添加 loading 状态
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        keyword: '',
        status: '',
        settleStatus: ''
      },
      tableData: {
        list: [],
        total: 0
      },
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
        showTotal: total => `共 ${total} 条`,
        showQuickJumper: true
      },
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          fixed: 'left',
          width: 80
        },
        {
          title: '单号',
          dataIndex: 'followNo',
          fixed: 'left',
          width: 160
        },
        {
          title: '跟单会员',
          children: [
            { title: '账号', dataIndex: 'userInfo.userAccount', width: 140 },
            { title: '姓名', dataIndex: 'userInfo.userName', width: 120 },
            { title: '手机号', dataIndex: 'userInfo.userPhone', width: 140 },
            { title: '跟单金额', dataIndex: 'userInfo.amount', width: 120 }
          ]
        },
        {
          title: '导师信息',
          children: [
            { title: '用户名', dataIndex: 'mentorInfo.mentorAccount', width: 140 },
            { title: '姓名', dataIndex: 'mentorInfo.mentorName', width: 120 },
            { title: '手机号', dataIndex: 'mentorInfo.mentorPhone', width: 140 },
            {
              title: '跟单限额',
              width: 160,
              customRender: function (_, row) {
                const min = row.mentorInfo?.minAmount ?? '';
                const max = row.mentorInfo?.maxAmount ?? '';
                return min && max ? `${min}-${max}` : '';
              }
            },
            {
              title: '佣金比例%',
              width: 120,
              customRender: function (_, row) {
                const val = row.mentorInfo?.salaryRate;
                return val !== undefined && val !== null ? val + '%' : '';
              }
            }
          ]
        },
        {
          title: '跟单订单详情',
          children: [
            { title: '股票代码', dataIndex: 'stockInfo.stockCode', width: 120 },
            { title: '股票名称', dataIndex: 'stockInfo.stockName', width: 120 },
            {
              title: '买入数量/现有数量/卖出数量',
              width: 180,
              customRender: function (_, row) {
                const buy = row.stockInfo?.buyQuantity ?? '';
                const cur = row.stockInfo?.currentQuantity ?? '';
                const sell = row.stockInfo?.sellQuantity ?? '';
                return `${buy}/${cur}/${sell}`;
              }
            },
            {
              title: '买入单价/卖出单价',
              width: 150,
              customRender: function (_, row) {
                const buy = row.stockInfo?.buyPrice ?? '';
                const sell = row.stockInfo?.sellPrice ?? '';
                return `${buy}/${sell}`;
              }
            },
            {
              title: '买入金额/卖出金额',
              width: 150,
              customRender: function (_, row) {
                const buy = row.stockInfo?.buyAmount ?? '';
                const sell = row.stockInfo?.sellAmount ?? '';
                return `${buy}/${sell}`;
              }
            },
            {
              title: '投顾收益',
              width: 120,
              customRender: function (_, row) {
                const val = row.stockInfo?.advisorProfit;
                return val !== undefined && val !== null ? val : '-';
              }
            },
            {
              title: '投顾收益比例(%)',
              width: 140,
              customRender: function (_, row) {
                const val = row.stockInfo?.advisorProfitRate;
                if (val !== undefined && val !== null) {
                  return (val * 100).toFixed(2) + '%';
                }
                return '-';
              }
            },
            {
              title: '建仓/平仓时间',
              width: 180,
              customRender: function (_, row) {
                const open = row.stockInfo.buyTime ? row.stockInfo.buyTime : '-';
                const close = row.stockInfo.sellTime ? row.stockInfo.sellTime : '-';
                return `${open} / ${close}`;
              }
            },
            {
              title: '结算时间',
              dataIndex: 'settlementTime',
              width: 160,
              customRender: function (text) { return text ?? '-'; }
            }
          ]
        },
        {
          title: '套餐类型',
          dataIndex: 'packageType',
          width: 100,
          scopedSlots: { customRender: 'packageType' }
        },
        {
          title: '结算状态',
          dataIndex: 'settlementStatus',
          width: 100,
          scopedSlots: { customRender: 'settlementStatus' }
        },
        {
          title: '状态',
          dataIndex: 'status',
          width: 100,
          scopedSlots: { customRender: 'status' }
        },

        {
          title: '操作',
          key: 'action',
          fixed: 'right',
          width: 100,
          scopedSlots: { customRender: 'action' }
        }
      ],
      detailDialogVisible: false,
      detailData: null,
      editDialogVisible: false,
      editForm: {
        stockInfo: {
          buyPrice: '',
          sellPrice: ''
        }
      }
    }
  },
  created() {
    this.fetchList()
  },
  methods: {
    fetchList() {
      // 设置加载状态为 true
      this.loading = true;

      pageList(this.queryParams).then(res => {
        if (res && res.data) {
          this.tableData.list = res.data.list
          this.tableData.total = res.data.total
          this.pagination.total = res.data.total
        }
      }).catch(error => {
        console.error('获取数据失败:', error);
        this.$message.error('获取数据失败，请稍后重试');
      }).finally(() => {
        // 无论成功还是失败，都将加载状态设置为 false
        this.loading = false;
      });
    },
    handleTableChange(pagination) {
      this.queryParams.pageNum = pagination.current
      this.queryParams.pageSize = pagination.pageSize
      this.pagination.current = pagination.current
      this.pagination.pageSize = pagination.pageSize
      this.fetchList()
    },
    showDetail(row) {
      this.detailData = row
      this.detailDialogVisible = true
    },
    showEdit(row) {
      this.editForm = { ...row };
      console.log(this.editForm, "rr");
      if (!this.editForm.stockInfo) {
        this.editForm.stockInfo = {};
      }
      if (this.editForm.stockInfo.buyPrice === undefined) {
        this.editForm.stockInfo.buyPrice = '';
      }
      if (this.editForm.stockInfo.sellPrice === undefined) {
        this.editForm.stockInfo.sellPrice = '';
      }
      this.editDialogVisible = true;
    },
    submitEdit() {
      // 创建一个新对象，只包含需要的字段
      const submitData = {
        id: this.editForm.id,
        buyPrice: this.editForm.stockInfo.buyPrice,
        sellPrice: this.editForm.stockInfo.sellPrice,
        openTime: this.editForm.openTime,
        closeTime: this.editForm.closeTime
      }

      // 设置加载状态
      this.loading = true;

      update(submitData).then(() => {
        this.$message.success('更新成功')
        this.editDialogVisible = false
        // 重新加载数据
        this.fetchList()
      }).catch(error => {
        console.error('更新失败:', error);
        this.$message.error('更新失败，请稍后重试');
      }).finally(() => {
        // 无论成功还是失败，都将加载状态设置为 false
        this.loading = false;
      });
    },
  }
}
</script>