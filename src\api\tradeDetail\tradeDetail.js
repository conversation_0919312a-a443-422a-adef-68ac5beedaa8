import request from '@/utils/request';

/**
 * 分页查询跟单明细列表
 * @param parameter 请求参数
 * @returns {*}
 */
export function pageList(parameter) {
  return request({
    url: '/agent/follow/detail/list.do',
    method: 'get',
    params: parameter
  });
}

/**
 * 获取跟单明细详情
 * @param parameter
 * @returns {*}
 */
export function getDetail(parameter) {
  return request({
    url: '/agent/follow/detail/detail.do',
    method: 'post',
    data: parameter
  });
}

/**
 * 更新跟单明细
 * @param parameter
 * @returns {*}
 */
export function update(parameter) {
  return request({
    url: '/agent/follow/detail/update.do',
    method: 'post',
    headers: {
      'Content-Type': 'application/json' // Added Content-Type header
    },
    data: parameter
  });
}

