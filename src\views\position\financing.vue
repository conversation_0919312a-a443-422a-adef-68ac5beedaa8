<template>
  <page-header-wrapper>
    <a-card>
      <a-tabs default-active-key="0" @change="handleTabChange">
        <a-tab-pane key="0" tab="股票委托">
          <financingPending ref="financingPending"></financingPending>
        </a-tab-pane>
        <a-tab-pane key="1" tab="股票持仓单" :forceRender="false">
          <financingHold ref="financingHold" v-if="activeKey === '1'"></financingHold>
        </a-tab-pane>
        <a-tab-pane key="2" tab="股票平仓单" :forceRender="false">
          <financingFlat ref="financingFlats" v-if="activeKey === '2'"></financingFlat>
        </a-tab-pane>
      </a-tabs>
    </a-card>
  </page-header-wrapper>
</template>

<script>
import financingHold from './components/financing-hold'
import financingPending from './components/financing-pending'
import financingFlat from './components/financing-flat'

export default {
  name: 'financing',
  components: {
    financingHold,
    financingPending,
    financingFlat
  },
  data() {
    return {
      activeKey: '0' // 当前激活的标签页
    }
  },
  methods: {
    handleTabChange(key) {
      this.activeKey = key
      // 根据不同的标签页调用相应的初始化方法
      if (key === '0') {
        this.$refs.financingPending?.getInit(2)
      } else if (key === '1') {
        this.$refs.financingHold?.getInit(0)
      } else if (key === '2') {
        this.$refs.financingFlats?.getInit(1)
      }
    }
  },
}
</script>

<style lang="less" scoped>
/deep/ .ant-tabs-nav-scroll {
  display: flex;
  justify-content: center;
}
</style>
