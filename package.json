{"name": "vue-antd-pro", "version": "3.0.4", "private": true, "scripts": {"dev": "cross-env APP_ENV=dev vue-cli-service serve", "dev:prod": "cross-env APP_ENV=prod vue-cli-service serve", "build:test": "cross-env APP_ENV=test vue-cli-service build --no-module", "build:prod": "cross-env APP_ENV=prod vue-cli-service build --no-module", "build:preview": "vue-cli-service build --no-module --mode preview"}, "dependencies": {"@ant-design-vue/pro-layout": "^1.0.12", "@antv/data-set": "^0.10.2", "ant-design-vue": "^1.7.8", "axios": "^0.26.1", "core-js": "^3.21.1", "js-file-download": "^0.4.12", "lodash.clonedeep": "^4.5.0", "lodash.get": "^4.4.2", "lodash.pick": "^4.4.0", "moment": "^2.29.2", "nprogress": "^0.2.0", "qs": "^6.11.0", "store": "^2.0.12", "vue": "^2.6.14", "vue-clipboard2": "^0.2.1", "vue-cropper": "0.4.9", "vue-router": "^3.5.3", "vue-template-compiler": "^2.6.14", "vuex": "^3.6.2"}, "devDependencies": {"@ant-design/colors": "^3.2.2", "@vue/cli-plugin-babel": "~5.0.8", "@vue/cli-service": "~5.0.8", "babel-plugin-import": "^1.13.3", "cross-env": "^7.0.3", "less": "^3.13.1", "less-loader": "^5.0.0", "regenerator-runtime": "^0.13.9", "vue-svg-icon-loader": "^2.1.1", "vue-svg-loader": "0.16.0", "webpack-theme-color-replacer": "^1.3.26"}}