import request from '@/utils/request'
import qs from 'qs'
const userApi = {
  subscribeList: '/agent/subscribe/list.do', // 新股列表
  subscribeAdd: '/agent/subscribe/add.do', // 新股添加
  subscribeUpdate: '/agent/subscribe/update.do', // 新股修改
  subscribeDel: '/agent/subscribe/del.do', // 新股删除
  getStockSubscribeList: 'agent/subscribe/getStockSubscribeList.do', // 新股记录列表
  saveStockSubscribe: '/agent/subscribe/saveStockSubscribe.do', // 新股记录添加 修改
  delStockSubscribe: 'agent/subscribe/delStockSubscribe.do', // 新股记录删除
  addUserPosition: '/agent/position/addUserPosition.do', // 新股转持仓
  getStockSubscribeQcListByagent: '/agent/subscribe/getStockSubscribeQcListByagent.do', // 新股抢筹记录列表
  addStockSubscribeQcByagent: '/agent/subscribe/addStockSubscribeQcByagent.do', // 新股抢筹记录添加
  updateStockSubscribeQcByagent: 'agent/subscribe/updateStockSubscribeQcByagent.do', // 新股抢筹记录修改
  getDzListByagent: '/agent/stockDz/getDzListByagent.do', // 大宗交易列表
  addByagent: '/agent/stockDz/addByagent.do', // 大宗交易添加
  updateByagent: '/agent/stockDz/updateByagent.do', // 大宗交易修改
  deleteByagent: '/agent/stockDz/deleteByagent.do', // 大宗交易删除
}

/**
 * login func
 * parameter: {
 *     username: '',
 *     password: '',
 *     remember_me: true,
 *     captcha: '12345'
 * }
 * @param parameter
 * @returns {*}
 */

export function subscribeList(parameter) {
  return request({
    url: userApi.subscribeList,
    method: 'post',
    data: qs.stringify(parameter),
  })
}

export function subscribeAdd(parameter) {
  return request({
    url: userApi.subscribeAdd,
    method: 'post',
    data: qs.stringify(parameter),
  })
}

export function subscribeUpdate(parameter) {
  return request({
    url: userApi.subscribeUpdate,
    method: 'post',
    data: qs.stringify(parameter),
  })
}

export function subscribeDel(parameter) {
  return request({
    url: userApi.subscribeDel,
    method: 'post',
    data: qs.stringify(parameter),
  })
}

export function getStockSubscribeList(parameter) {
  return request({
    url: userApi.getStockSubscribeList,
    method: 'post',
    data: qs.stringify(parameter),
  })
}

export function saveStockSubscribe(parameter) {
  return request({
    url: userApi.saveStockSubscribe,
    method: 'post',
    data: qs.stringify(parameter),
  })
}

export function delStockSubscribe(parameter) {
  return request({
    url: userApi.delStockSubscribe,
    method: 'post',
    data: qs.stringify(parameter),
  })
}

export function addUserPosition(parameter) {
  return request({
    url: userApi.addUserPosition,
    method: 'post',
    data: qs.stringify(parameter),
  })
}

export function getStockSubscribeQcListByagent(parameter) {
  return request({
    url: userApi.getStockSubscribeQcListByagent,
    method: 'post',
    data: qs.stringify(parameter),
  })
}

export function addStockSubscribeQcByagent(parameter) {
  return request({
    url: userApi.addStockSubscribeQcByagent,
    method: 'post',
    data: qs.stringify(parameter),
  })
}

export function updateStockSubscribeQcByagent(parameter) {
  return request({
    url: userApi.updateStockSubscribeQcByagent,
    method: 'post',
    data: qs.stringify(parameter),
  })
}

export function getDzListByagent(parameter) {
  return request({
    url: userApi.getDzListByagent,
    method: 'post',
    data: qs.stringify(parameter),
  })
}

export function addByagent(parameter) {
  return request({
    url: userApi.addByagent,
    method: 'post',
    data: qs.stringify(parameter),
  })
}

export function updateByagent(parameter) {
  return request({
    url: userApi.updateByagent,
    method: 'post',
    data: qs.stringify(parameter),
  })
}

export function deleteByagent(parameter) {
  return request({
    url: userApi.deleteByagent,
    method: 'post',
    data: qs.stringify(parameter),
  })
}