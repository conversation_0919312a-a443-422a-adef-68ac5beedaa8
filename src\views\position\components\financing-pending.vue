<template>
    <div>
        <a-card :bordered="false">
            <div class="table-page-search-wrapper">
                <a-form layout="inline">
                    <a-row :gutter="48">
                        <a-col :md="12" :lg="6" :sm="24">
                            <a-form-item label="用户Id">
                                <a-input v-model="queryParam.userId" style="width: 100%" placeholder="请输入用户Id" />
                            </a-form-item>
                        </a-col>
                      <a-col :md="12" :lg="6" :sm="24">
                        <a-form-item>
                                <span class="table-page-search-submitButtons">
                                    <a-button @click="getQueryParam" icon="redo">
                                        重置</a-button>
                                    <a-button type="primary" icon="search" style="margin-left: 8px"
                                              @click="queryParam.pageNum = 1, getList()">查询
                                    </a-button>
                                </span>
                        </a-form-item>
                      </a-col>
                    </a-row>
                </a-form>
            </div>
        </a-card>
        <a-table bordered :loading="loading" :pagination="pagination" :columns="columns" :data-source="datalist"
            rowKey="id" :scroll="{ x: 830 }" :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }">

            <span slot="stockName" slot-scope="text,record">
                <template>
                    <div>
                        <span style="margin-right:10px">{{ record.stockName }}</span>
                        <a-tag
                            :color="record.stockPlate == '科创' ? 'blue' : !record.stockPlate ? 'orange' : record.stockPlate == '创业' ? 'pink' : 'purple'">
                            {{ record.stockPlate == '科创' ? '科创' : !record.stockPlate ? '股票' : record.stockPlate }}
                        </a-tag>
                        <p>({{ record.stockCode }})</p>
                    </div>
                </template>
            </span>
            <span slot="orderDirection" slot-scope="text,record">
                <template>
                    <div>
                        <a-tag :color="record.orderDirection == '买涨' ? 'red' : 'green'">
                            {{ record.orderDirection }}
                        </a-tag>
                    </div>
                </template>
            </span>
            <span slot="now_price" slot-scope="text,record">
                <template>
                    <div>
                        <p
                            :class="Number(record.now_price) - record.buyOrderPrice < 0 ? 'greens' : Number(record.now_price) - record.buyOrderPrice > 0 ? 'reds' : ''">
                            {{ record.now_price }}
                        </p>
                    </div>
                </template>
            </span>
            <span slot="profitAndLose" slot-scope="text">
                <template>
                    <div>
                        <p :class="text < 0 ? 'greens' : text > 0 ? 'reds' : ''">
                            {{ text }}
                        </p>
                    </div>
                </template>
            </span>
            <span slot="allProfitAndLose" slot-scope="text">
                <template>
                    <div>
                        <p :class="text < 0 ? 'greens' : text > 0 ? 'reds' : ''">
                            {{ text }}
                        </p>
                    </div>
                </template>
            </span>
        </a-table>

    </div>
</template>
<script>
import { positionList } from '@/api/position'
export default {
    name: 'financingpending',
    data() {
        return {
            columns: [
                {
                    title: '融资名称',
                    dataIndex: 'stockName',
                    align: 'center',
                    width: 120,
                },
                {
                    title: '用户名称',
                    dataIndex: 'nickName',
                    align: 'center',
                    width: 120,
                },
                {
                    title: '单号',
                    dataIndex: 'positionSn',
                    align: 'center',
                    width: 180,
                },
                {
                    title: '类型',
                    dataIndex: 'buyType',
                    align: 'center',
                    width: 70,
                    customRender: (text) => text === 0 ? '买' : '卖',
                },
                {
                    title: '目标价格',
                    dataIndex: 'buyOrderPrice',
                    align: 'center',
                    width: 100,
                },
                {
                    title: '数量',
                    dataIndex: 'orderNum',
                    align: 'center',
                    width: 80,
                },
                {
                    title: '创建时间',
                    dataIndex: 'createTime',
                    align: 'center',
                    width: 160,
                },
            ],
            pagination: {
                total: 0,
                pageSize: 10,
                showSizeChanger: true,
                pageSizeOptions: ["10", "20", "50", "100"],
                onShowSizeChange: (current, pageSize) => this.onSizeChange(current, pageSize),
                onChange: (page, pageSize) => this.onPageChange(page, pageSize),
                showTotal: total => `共有 ${total} 条数据`,
            },
            loading: false,
            queryParam: {
                pageNum: 1,
                pageSize: 10,
                userId: '',
                positionSn: '',
                state: 2,
            },
            datalist: [],
            LockVisibleDialog: false,
            LockVisibleLoading: false,
            LockVisibleForm: this.$form.createForm(this),
            clickPositionId: '',
            selectedRowKeys:[]
        }
    },
    created() {
        this.getInit(2)
    },
    methods: {
        getInit(state = 2) {
            this.queryParam = {
                pageNum: 1,
                pageSize: 10,
                userId: '',
                positionSn: '',
                state: state,
            }
            this.getList()
        },
        getQueryParam() {
            this.queryParam = {
                pageNum: 1,
                pageSize: 10,
                userId: '',
                positionSn: '',
                state: 2,
            }
        },
        getList() {
            this.loading = true
            positionList(this.queryParam).then(res => {
                this.datalist = res.data.list
                this.pagination.total = res.data.total
                this.loading = false
            })
        },
        onPageChange(page, pageSize) {
            this.queryParam.pageNum = page
            this.getList()
        },
        onSizeChange(current, pageSize) {
            this.queryParam.pageNum = current
            this.queryParam.pageSize = pageSize
            this.getList()
        },
        onSelectChange(selectedRowKeys) {
            this.selectedRowKeys = selectedKeys;
        },
      handleCancel() {

      }
    }
}
</script>
<style scoped>
.greens {
    color: #52c41a;
}

.reds {
    color: #f5222d;
}
</style>
