import request from '@/utils/request'
import qs from 'qs'

const userApi = {
  login: '/api/agent/login.do',  // 登录接口
  getInfo: '/api/agent/info.do',  // 获取用户信息
  logout: '/api/agent/logout.do',  // 退出登录
}

/**
 * 登录接口
 * @param parameter
 * @returns {*}
 */
export function login(parameter) {
  return request({
    url: userApi.login,
    method: 'post',
    data: qs.stringify(parameter)
  })
}

/**
 * 获取用户信息
 * @returns {*}
 */
export function getInfo() {
  return request({
    url: userApi.getInfo,
    method: 'post',
  })
}

/**
 * 退出登录
 * @returns {*}
 */
export function logout() {
  return request({
    url: userApi.logout,
    method: 'post',
  })
}

/**
 * get user 2step code open?
 * @param parameter {*}
 */
export function get2step(parameter) {
  return new Promise((resolve) => {
    resolve({
      status: 0,
      result: false
    })
  })
}
