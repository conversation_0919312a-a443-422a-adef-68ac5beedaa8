import { mockUserInfo } from '@/utils/mockData'

/**
 * login func
 * parameter: {
 *     username: '',
 *     password: '',
 *     remember_me: true,
 *     captcha: '12345'
 * }
 * @param parameter
 * @returns {*}
 */
export function login(parameter) {
  return new Promise((resolve) => {
    // 模拟登录验证
    if (parameter.username === 'admin' && parameter.password === 'admin') {
      resolve({
        status: 0,
        data: {
          token: '4291d7da9005377ec9aec4a71ea837f',
          expireTime: Date.now() + 24 * 60 * 60 * 1000 // 24小时后过期
        },
        msg: '登录成功'
      })
    } else {
      resolve({
        status: 1,
        msg: '用户名或密码错误'
      })
    }
  })
}

export function getSmsCaptcha(parameter) {
  return new Promise((resolve) => {
    resolve({
      status: 0,
      msg: '验证码发送成功'
    })
  })
}

export function getInfo() {
  return new Promise((resolve) => {
    resolve({
      result: mockUserInfo
    })
  })
}

export function getCurrentUserNav() {
  return new Promise((resolve) => {
    resolve({
      result: []
    })
  })
}

export function logout() {
  return new Promise((resolve) => {
    resolve({
      status: 0,
      msg: '退出成功'
    })
  })
}

/**
 * get user 2step code open?
 * @param parameter {*}
 */
export function get2step(parameter) {
  return new Promise((resolve) => {
    resolve({
      status: 0,
      result: false
    })
  })
}
