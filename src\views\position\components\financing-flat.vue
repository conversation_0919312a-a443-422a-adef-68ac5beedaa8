<template>
    <div>
        <a-card :bordered="false">
            <div class="table-page-search-wrapper">
                <a-form layout="inline">
                    <a-row :gutter="48">
                        <a-col :md="12" :lg="6" :sm="24">
                            <a-form-item label="用户手机">
                                <a-input v-model="queryParam.phone" placeholder="请输入用户手机" />
                            </a-form-item>
                        </a-col>
                        <a-col :md="12" :lg="6" :sm="24">
                            <a-form-item label="用户姓名">
                                <a-input v-model="queryParam.realName" placeholder="请输入用户姓名" />
                            </a-form-item>
                        </a-col>
                        <a-col :md="12" :lg="6" :sm="24">
                            <a-form-item label="持仓订单号">
                                <a-input v-model="queryParam.positionSn" style="width: 100%" placeholder="请输入持仓订单号" />
                            </a-form-item>
                        </a-col>
                    </a-row>
                    <a-row :gutter="48">
                        <a-col :md="12" :lg="8" :sm="24">
                            <a-form-item label="卖出时间">
                                <a-range-picker show-time style="width: 100%" v-model="times"
                                    @change="onChangeRangeDate" format="YYYY-MM-DD HH:mm:ss">
                                </a-range-picker>
                            </a-form-item>
                        </a-col>

                        <a-col :md="12" :lg="6" :sm="24">
                            <a-form-item>
                                <span class="table-page-search-submitButtons">
                                    <a-button @click="getQueryParam" icon="redo">
                                        重置</a-button>
                                    <a-button type="primary" icon="search" style="margin-left: 8px"
                                        @click="queryParam.pageNum = 1, getList()">查询
                                    </a-button>
                                </span>
                            </a-form-item>
                        </a-col>
                    </a-row>
                </a-form>
            </div>
        </a-card>
        <a-table bordered :loading="loading" :pagination="pagination" :columns="columns" :data-source="datalist"
            rowKey="id" :scroll="{ x: 1100 }">
            <span slot="stockName" slot-scope="text,record">
                <template>
                    <div>
                        <span style="margin-right:10px">{{ record.stockName }}</span>
                        <a-tag
                            :color="record.stockPlate == '科创' ? 'blue' : !record.stockPlate ? 'orange' : record.stockPlate == '创业' ? 'pink' : 'purple'">
                            {{ record.stockPlate == '科创' ? '科创' : !record.stockPlate ? '股票' : record.stockPlate }}
                        </a-tag>
                        <p>({{ record.stockCode }})</p>
                    </div>
                </template>
            </span>
            <span slot="positionType" slot-scope="text,record">
                <template>
                    <div>
                        <a-tag :color="record.positionType == 1 ? 'blue' : 'green'">
                            {{ record.positionType == 1 ? '模拟持仓' : '正式持仓' }}
                        </a-tag>
                    </div>
                </template>
            </span>
            <span slot="orderDirection" slot-scope="text,record">
                <template>
                    <div>
                        <a-tag :color="record.orderDirection == '买涨' ? 'red' : 'green'">
                            {{ record.orderDirection }}
                        </a-tag>
                    </div>
                </template>
            </span>
            <span slot="profitAndLose" slot-scope="text">
                <template>
                    <div>
                        <p :class="text < 0 ? 'greens' : text > 0 ? 'reds' : ''">
                            {{ text }}
                        </p>
                    </div>
                </template>
            </span>

            <span slot="allProfitAndLose" slot-scope="text">
                <template>
                    <div>
                        <p :class="text < 0 ? 'greens' : text > 0 ? 'reds' : ''">
                            {{ text }}
                        </p>
                    </div>
                </template>
            </span>
            <template slot="action" slot-scope="text,record">
                <a slot="action" href="javascript:;" @click="getFlatDetails(record)">查看详情</a>
                <a-divider type="vertical" />
            </template>
        </a-table>
        <a-modal title="融资详情" :width="1000" :visible="financingDialog" :footer="false" @cancel="handleCancel">
            <a-descriptions bordered :title="`${clickItem.stockName}(${clickItem.stockCode})`">
                <a-descriptions-item label="用户名称（ID）">
                    <span>{{ clickItem.nickName }}（{{ clickItem.userId }}）</span>
                </a-descriptions-item>
                <a-descriptions-item label="股票类型">
                    <a-tag
                        :color="clickItem.stockPlate == '科创' ? 'blue' : !clickItem.stockPlate ? 'orange' : clickItem.stockPlate == '创业' ? 'pink' : 'purple'">
                      {{ clickItem.stockPlate == '科创' ? '科创' : !clickItem.stockPlate ? 'A股' : clickItem.stockPlate
                      }}
                    </a-tag>
                </a-descriptions-item>
                <a-descriptions-item label="持仓ID">
                    <span>
                        {{ clickItem.id }}
                    </span>
                </a-descriptions-item>
                <a-descriptions-item label="浮动盈亏">
                    <span :class="clickItem.profitAndLose > 0 ? 'reds' : clickItem.profitAndLose < 0 ? 'greens' : ''">
                        {{ clickItem.profitAndLose }}
                    </span>
                </a-descriptions-item>
                <a-descriptions-item label="总盈亏">
                    <span
                        :class="clickItem.allProfitAndLose > 0 ? 'reds' : clickItem.allProfitAndLose < 0 ? 'greens' : ''">
                        {{ clickItem.allProfitAndLose }}
                    </span>
                </a-descriptions-item>
                <a-descriptions-item label="当前价格" v-if="clickItem.now_price">
                    <span
                        :class="clickItem.now_price - clickItem.buyOrderPrice > 0 ? 'reds' : clickItem.now_price - clickItem.buyOrderPrice < 0 ? 'greens' : ''">
                        {{ clickItem.now_price }}
                    </span>
                </a-descriptions-item>
                <a-descriptions-item label="卖出价格">
                    <span>
                        {{ clickItem.sellOrderPrice }}
                    </span>
                </a-descriptions-item>
                <a-descriptions-item label="买入价格">
                    <span>
                        {{ clickItem.buyOrderPrice }}
                    </span>
                </a-descriptions-item>
                <a-descriptions-item label="买入数量">
                    <span>
                        {{ clickItem.orderNum }}
                    </span>
                </a-descriptions-item>
                <a-descriptions-item label="买卖方向">
                    <a-tag :color="clickItem.orderDirection == '买涨' ? 'red' : 'green'">
                      {{ clickItem.orderDirection }}
                    </a-tag>
                </a-descriptions-item>
                <a-descriptions-item label="总市值">
                    <span>
                        {{ clickItem.orderTotalPrice }}
                    </span>
                </a-descriptions-item>
                <a-descriptions-item label="买入时间">
                    <span>
                        {{ clickItem.buyOrderTime | moment }}
                    </span>
                </a-descriptions-item>
                <a-descriptions-item label="卖出时间">
                    <span>
                        {{ clickItem.sellOrderTime | moment }}
                    </span>
                </a-descriptions-item>
                <a-descriptions-item label="买入订单号">
                  {{ clickItem.buyOrderId }}
                </a-descriptions-item>
                <a-descriptions-item label="卖出订单号">
                  {{ clickItem.sellOrderId }}
                </a-descriptions-item>
                <a-descriptions-item label="持仓订单号">
                  {{ clickItem.positionSn }}
                </a-descriptions-item>
                <a-descriptions-item label="锁仓原因">
                  {{ clickItem.lockMsg }}
                </a-descriptions-item>
            </a-descriptions>
        </a-modal>
    </div>
</template>
<script>
import { positionList, positionDel } from '@/api/position'
import { nextAgent } from '@/api/home'
import moment from 'moment'
export default {
    name: 'financingflat',
    data() {
        return {
            columns: [
                {
                    title: '融资名称',
                    dataIndex: 'stockName',
                    align: 'center',
                    width: 100,
                    scopedSlots: { customRender: 'stockName' },
                },
                {
                    title: '用户名称（ID）',
                    dataIndex: 'nickName',
                    align: 'center',
                    width: 120,
                    customRender: (text, row, index) => {
                        return `${row.nickName}（${row.userId}）`
                    }
                },
                {
                    title: '持仓订单号（ID）',
                    dataIndex: 'positionSn',
                    align: 'center',
                    width: 150,
                    customRender: (text, row, index) => {
                        return `${row.positionSn}（${row.id}）`
                    }
                },
                {
                    title: '买卖方向',
                    dataIndex: 'orderDirection',
                    align: 'center',
                    width: 70,
                    scopedSlots: { customRender: 'orderDirection' },
                },
                {
                    title: '买入价',
                    dataIndex: 'buyOrderPrice',
                    align: 'center',
                    width: 70,
                    customRender: (text, row, index) => {
                        return text.toFixed(2)
                    }
                },
                {
                    title: '卖出价',
                    dataIndex: 'sellOrderPrice',
                    align: 'center',
                    width: 70,
                    customRender: (text, row, index) => {
                        return text.toFixed(2)
                    }
                },
                {
                    title: '浮动盈亏',
                    dataIndex: 'profitAndLose',
                    align: 'center',
                    width: 80,
                    scopedSlots: { customRender: 'profitAndLose' },
                },
                {
                    title: '总盈亏',
                    dataIndex: 'allProfitAndLose',
                    align: 'center',
                    width: 80,
                    scopedSlots: { customRender: 'allProfitAndLose' },
                },
                {
                    title: '数量（股）',
                    dataIndex: 'orderNum',
                    align: 'center',
                    width: 70,
                },
                {
                    title: '总市值',
                    dataIndex: 'orderTotalPrice',
                    align: 'center',
                    width: 80,
                },
                {
                    title: '手续费',
                    dataIndex: 'orderFee',
                    align: 'center',
                    width: 70,
                },
                {
                    title: '买入时间',
                    dataIndex: 'buyOrderTime',
                    align: 'center',
                    width: 110,
                    customRender: (text, row, index) => {
                        return text ? moment(text).format('YYYY-MM-DD HH:mm:ss') : ''
                    }
                },
                {
                    title: '卖出时间',
                    dataIndex: 'sellOrderTime',
                    align: 'center',
                    width: 110,
                    customRender: (text, row, index) => {
                        return text ? moment(text).format('YYYY-MM-DD HH:mm:ss') : ''
                    }
                },
                {
                    title: '操作',
                    key: 'action',
                    align: 'center',
                    fixed: 'right',
                    width: 90,
                    scopedSlots: { customRender: 'action' },
                },
            ],
            //表头
            pagination: {
                total: 0,
                pageSize: 10,//每页中显示10条数据
                showSizeChanger: true,
                pageSizeOptions: ["10", "20", "50", "100"],//每页中显示的数据
                onShowSizeChange: (current, pageSize) => this.onSizeChange(current, pageSize), // 改变每页数量时更新显示
                onChange: (page, pageSize) => this.onPageChange(page, pageSize),//点击页码事件
                showTotal: total => `共有 ${total} 条数据`,  //分页中显示总的数据
            },
            loading: false,
            queryParam: {
                pageNum: 1,
                pageSize: 10,
                agentId: undefined,
                userId: '',
                positionSn: '',
                state: 1,
                beginTime: '',
                endTime: '',
            },
            datalist: [],
            agentList: [],
            agentLoading: false,
            times: [],
            financingDialog: false,
            clickItem: {},
            agentQueryParam: {
                pageNum: 1,
                pageSize: 100,
            },
        }
    },
    created() {
        this.getList()
    },
    methods: {
        getFlatDetails(item) {
            this.clickItem = item
            this.financingDialog = true
        },
        getInit(state = 1) {
            this.queryParam = {
                pageNum: 1,
                pageSize: 10,
                agentId: undefined,
                userId: '',
                positionSn: '',
                state: 1,
                beginTime: '',
                endTime: '',
            }
            this.times = []
            this.getList()
        },
        onChangeRangeDate(value, dateString) {
            this.queryParam.beginTime = dateString[0]
            this.queryParam.endTime = dateString[1]
        },
        getQueryParam() {
            this.queryParam = {
                pageNum: 1,
                pageSize: 10,
                agentId: undefined,
                userId: '',
                positionSn: '',
                state: 1,
                beginTime: '',
                endTime: '',
            }
            this.times = []
        },

        getList() {
            var that = this;
            console.log('getList called', this.queryParam)
            this.loading = true
            positionList(this.queryParam).then(res => {
                this.datalist = res.data.list
                this.pagination.total = res.data.total
                setTimeout(() => {
                    that.loading = false
                }, 500)
            })
        },
        onPageChange(page, pageSize) {
            this.queryParam.pageNum = page
            this.getList()
        },
        onSizeChange(current, pageSize) {
            this.queryParam.pageNum = current
            this.queryParam.pageSize = pageSize
            this.getList()
        },
        handleCancel() {
            this.financingDialog = false
        },
    }
}
</script>
<style scoped>
.greens {
    color: #52c41a;
}

.reds {
    color: #f5222d;
}
</style>