import request from '@/utils/request'
import qs from 'qs'

export function agentuserlist(parameter) {
  return request({
    url: '/agent/user/list.do',
    method: 'post',
    data: qs.stringify(parameter)
  })
}

export function agentgetSecondAgent(parameter) {
  return new Promise((resolve) => {
    resolve({
      status: 0,
      data: {
        list: [
          {
            id: 1,
            agentCode: 'A001',
            agentName: '代理商001',
            phone: '***********',
            email: '<EMAIL>',
            balance: 50000.00,
            status: 1,
            createTime: '2023-01-01 09:00:00'
          }
        ],
        total: 1
      },
      msg: '获取成功'
    })
  })
}

export function agentaddSimulatedAccount(parameter) {
  return new Promise((resolve) => {
    resolve({
      status: 0,
      msg: '添加成功'
    })
  })
}

export function agentaddAgent(parameter) {
  return new Promise((resolve) => {
    resolve({
      status: 0,
      msg: '添加代理成功'
    })
  })
}

export function agentgetAgentInfo(parameter) {
  return request({
    url: '/agent/info.do',
    method: 'post',
    data: parameter ? qs.stringify(parameter) : undefined
  })
}

export function getAgentCountData(parameter) {
  return request({
    url: '/agent/countData.do',
    method: 'get',
    data: parameter ? qs.stringify(parameter) : undefined
  })
}

export function agentgetAgentAgencyFeeList(parameter) {
  return new Promise((resolve) => {
    resolve({
      status: 0,
      data: {
        list: [
          {
            id: 1,
            userId: 'U001',
            userName: '张三',
            profit: 120.50,
            fee: 12.05,
            createTime: '2023-12-01 10:00:00'
          },
          {
            id: 2,
            userId: 'U002',
            userName: '李四',
            profit: 85.30,
            fee: 8.53,
            createTime: '2023-12-01 11:00:00'
          }
        ],
        total: 2
      },
      msg: '获取成功'
    })
  })
}

export function agentupdatePwd(parameter) {
  return request({
    url: '/agent/updatePwd.do',
    method: 'post',
    data: qs.stringify(parameter)
  })
}
