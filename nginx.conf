
    server {
        listen 8080 default;
        root /usr/share/nginx/html;
        index  index.html index.htm;

        # 开启 Gzip 压缩
        gzip on;
        gzip_vary on;
        gzip_proxied any;
        gzip_comp_level 6;
        gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

        # 添加安全响应头
        add_header X-Frame-Options "SAMEORIGIN";
        add_header X-Content-Type-Options "nosniff";
        # 禁用自动索引
        autoindex off;

        location / {
            try_files $uri $uri/ /index.html;
            expires 365d;
        }
        location = /index.html {
            internal;
            add_header Cache-Control 'no-store';
        }
        error_page 404 /40x.html;
        location = /40x.html {
          internal;
          default_type text/plain;
          return 500 'System error';
        }
        error_page 500 502 503 504 /50x.html;
        location = /50x.html {
          internal;
          default_type text/plain;
          return 500 'System error';
        }
    }


