<template>
  <page-header-wrapper>
    <div class="dashboard-container">
      <!-- 团队数据 -->
      <a-row :gutter="[16, 16]">
        <a-col v-for="(item, index) in cardRows[0]" :key="index" :xs="24" :sm="12" :md="8" :lg="6" :xl="3">
          <a-card class="dashboard-card team-members-card">
            <div class="card-title">{{ item.title }}</div>
            <div class="card-content">
              <div class="today-value">今日: {{ userdetail[`today${item.key}`] || 0 }}</div>
              <div class="total-value">总计: {{ userdetail[`total${item.key}`] || 0 }}</div>
            </div>
          </a-card>
        </a-col>
      </a-row>
      <!-- 一代数据 -->
      <a-row :gutter="[16, 16]" style="margin-top: 16px;">
        <a-col v-for="(item, index) in cardRows[1]" :key="index" :xs="24" :sm="12" :md="8" :lg="6" :xl="3">
          <a-card class="dashboard-card first-gen-members-card">
            <div class="card-title">{{ item.title }}</div>
            <div class="card-content">
              <div class="today-value">今日: {{ userdetail[`today${item.key}`] || 0 }}</div>
              <div class="total-value">总计: {{ userdetail[`total${item.key}`] || 0 }}</div>
            </div>
          </a-card>
        </a-col>
      </a-row>
      <!-- 二代数据 -->
      <a-row :gutter="[16, 16]" style="margin-top: 16px;">
        <a-col v-for="(item, index) in cardRows[2]" :key="index" :xs="24" :sm="12" :md="8" :lg="6" :xl="3">
          <a-card class="dashboard-card second-gen-members-card">
            <div class="card-title">{{ item.title }}</div>
            <div class="card-content">
              <div class="today-value">今日: {{ userdetail[`today${item.key}`] || 0 }}</div>
              <div class="total-value">总计: {{ userdetail[`total${item.key}`] || 0 }}</div>
            </div>
          </a-card>
        </a-col>
      </a-row>
      <!-- 三代数据 -->
      <a-row :gutter="[16, 16]" style="margin-top: 16px;">
        <a-col v-for="(item, index) in cardRows[3]" :key="index" :xs="24" :sm="12" :md="8" :lg="6" :xl="3">
          <a-card class="dashboard-card third-gen-members-card">
            <div class="card-title">{{ item.title }}</div>
            <div class="card-content">
              <div class="today-value">今日: {{ userdetail[`today${item.key}`] || 0 }}</div>
              <div class="total-value">总计: {{ userdetail[`total${item.key}`] || 0 }}</div>
            </div>
          </a-card>
        </a-col>
      </a-row>
      <!-- 四代数据 -->
      <a-row :gutter="[16, 16]" style="margin-top: 16px;">
        <a-col v-for="(item, index) in cardRows[4]" :key="index" :xs="24" :sm="12" :md="8" :lg="6" :xl="3">
          <a-card class="dashboard-card fourth-gen-members-card">
            <div class="card-title">{{ item.title }}</div>
            <div class="card-content">
              <div class="today-value">今日: {{ userdetail[`today${item.key}`] || 0 }}</div>
              <div class="total-value">总计: {{ userdetail[`total${item.key}`] || 0 }}</div>
            </div>
          </a-card>
        </a-col>
      </a-row>
      <!-- 五代数据 -->
      <a-row :gutter="[16, 16]" style="margin-top: 16px;">
        <a-col v-for="(item, index) in cardRows[5]" :key="index" :xs="24" :sm="12" :md="8" :lg="6" :xl="3">
          <a-card class="dashboard-card fifth-gen-members-card">
            <div class="card-title">{{ item.title }}</div>
            <div class="card-content">
              <div class="today-value">今日: {{ userdetail[`today${item.key}`] || 0 }}</div>
              <div class="total-value">总计: {{ userdetail[`total${item.key}`] || 0 }}</div>
            </div>
          </a-card>
        </a-col>
      </a-row>

      <!-- 代理信息卡片 -->
      <a-card :bordered="false" style="margin-top: 16px;" :title="`【账号余额：${userdetail.totalMoney ? userdetail.totalMoney : '0.00'}】`">
        <div class="everylist">
          <a-row :gutter="48">
            <a-col :md="12" :lg="8" :sm="24">
              代理名称：{{ userdetail.agentName }}
            </a-col>
            <a-col :md="12" :lg="8" :sm="24">
              真实姓名：{{ userdetail.agentRealName }}
            </a-col>
            <a-col :md="12" :lg="8" :sm="24">
              代理代码：{{ userdetail.agentCode }}
            </a-col>
          </a-row>
        </div>

        <div class="everylist">
          <a-row :gutter="48">
            <a-col :md="12" :lg="8" :sm="24">
              锁定状态：
              {{ userdetail.isLock == 0 ? '正常' : '锁定' }}
            </a-col>
            <a-col :md="12" :lg="8" :sm="24">
              电话号码：{{ userdetail.agentPhone }}
            </a-col>
            <a-col :md="12" :lg="8" :sm="24">
              创建时间：
              {{ userdetail.addTime | moment }}
            </a-col>
          </a-row>
        </div>

        <div class="everylist">
          链接（移动端）：{{ wapurl }}
          <a-button type="primary" @click="onCopy(wapurl)" icon="copy" size="small"
            style="margin-left: 10px;">复制</a-button>
        </div>
      </a-card>
    </div>
  </page-header-wrapper>
</template>

<script>
import { timeFix } from '@/utils/util'
import { mapState } from 'vuex'
import { PageHeaderWrapper } from '@ant-design-vue/pro-layout'
import axios from 'axios'
const DataSet = require('@antv/data-set')
export default {
  name: 'Workplace',
  components: {
    PageHeaderWrapper
  },
  data() {
    return {
      cardRows: [
        // 团队数据
        [
          { type: 'member', title: '团队成员', key: 'Members', defaultTotal: 6 },
          { type: 'active', title: '活跃人数', key: 'ActiveUsers' },
          { type: 'recharge', title: '团队充值', key: 'Recharge', unit: ' (人民币)', defaultTotal: 140000.00 },
          { type: 'withdraw', title: '团队提现', key: 'Withdraw', defaultTotal: 42032.04 },
          { type: 'profit', title: '团队利润', key: 'Profit', defaultTotal: 97957.96 },
          { type: 'follow', title: '跟单人数', key: 'Follows', defaultTotal: 2 },
          { type: 'smart-invest', title: '智投标的收益', key: 'SmartInvest', defaultTotal: 4489.32 }
        ],
        // 一代数据
        [
          { type: 'member', title: '一代成员', key: 'FirstGenMembers', defaultTotal: 6 },
          { type: 'active', title: '一代活跃人数', key: 'FirstGenActiveUsers' },
          { type: 'recharge', title: '一代充值', key: 'FirstGenRecharge', unit: ' (人民币)', defaultTotal: 140000.00 },
          { type: 'withdraw', title: '一代提现', key: 'FirstGenWithdraw', defaultTotal: 42032.04 },
          { type: 'profit', title: '一代利润', key: 'FirstGenProfit', defaultTotal: 97957.96 },
          { type: 'follow', title: '一代跟单人数', key: 'FirstGenFollows', defaultTotal: 2 },
          { type: 'smart-invest', title: '一代智投标的收益', key: 'FirstGenSmartInvest', defaultTotal: 4489.32 }
        ],
        // 二代数据
        [
          { type: 'member', title: '二代成员', key: 'SecondGenMembers' },
          { type: 'active', title: '二代活跃人数', key: 'SecondGenActiveUsers' },
          { type: 'recharge', title: '二代充值', key: 'SecondGenRecharge', unit: ' (人民币)' },
          { type: 'withdraw', title: '二代提现', key: 'SecondGenWithdraw' },
          { type: 'profit', title: '二代利润', key: 'SecondGenProfit' },
          { type: 'follow', title: '二代跟单人数', key: 'SecondGenFollows' },
          { type: 'smart-invest', title: '二代智投标的收益', key: 'SecondGenSmartInvest' }
        ],
        // 三代数据
        [
          { type: 'member', title: '三代成员', key: 'ThirdGenMembers' },
          { type: 'active', title: '三代活跃人数', key: 'ThirdGenActiveUsers' },
          { type: 'recharge', title: '三代充值', key: 'ThirdGenRecharge', unit: ' (人民币)' },
          { type: 'withdraw', title: '三代提现', key: 'ThirdGenWithdraw' },
          { type: 'profit', title: '三代利润', key: 'ThirdGenProfit' },
          { type: 'follow', title: '三代跟单人数', key: 'ThirdGenFollows' },
          { type: 'smart-invest', title: '三代智投标的收益', key: 'ThirdGenSmartInvest' }
        ],
        // 四代数据
        [
          { type: 'member', title: '四代成员', key: 'FourthGenMembers' },
          { type: 'active', title: '四代活跃人数', key: 'FourthGenActiveUsers' },
          { type: 'recharge', title: '四代充值', key: 'FourthGenRecharge', unit: ' (人民币)' },
          { type: 'withdraw', title: '四代提现', key: 'FourthGenWithdraw' },
          { type: 'profit', title: '四代利润', key: 'FourthGenProfit' },
          { type: 'follow', title: '四代跟单人数', key: 'FourthGenFollows' },
          { type: 'smart-invest', title: '四代智投标的收益', key: 'FourthGenSmartInvest' }
        ],
        // 五代及以下数据
        [
          { type: 'member', title: '五代及以下成员', key: 'FifthGenBelowMembers' },
          { type: 'active', title: '五代及以下活跃人数', key: 'FifthGenBelowActiveUsers' },
          { type: 'recharge', title: '五代及以下充值', key: 'FifthGenBelowRecharge', unit: ' (人民币)' },
          { type: 'withdraw', title: '五代及以下提现', key: 'FifthGenBelowWithdraw' },
          { type: 'profit', title: '五代及以下利润', key: 'FifthGenBelowProfit' },
          { type: 'follow', title: '五代及以下跟单人数', key: 'FifthGenBelowFollows' },
          { type: 'smart-invest', title: '五代及以下智投标的收益', key: 'FifthGenBelowSmartInvest' }
        ]
      ],
      pieScale: null,
      pieData: null,
      sourceData: null,
      pieStyle: {
        stroke: '#fff',
        lineWidth: 1
      },
      timeFix: timeFix(),
      avatar: '',
      user: {},

      projects: [],
      loading: true,
      radarLoading: true,
      activities: [],
      teams: [],

      // data
      axis1Opts: {
        dataKey: 'item',
        line: null,
        tickLine: null,
        grid: {
          lineStyle: {
            lineDash: null
          },
          hideFirstLine: false
        }
      },
      axis2Opts: {
        dataKey: 'score',
        line: null,
        tickLine: null,
        grid: {
          type: 'polygon',
          lineStyle: {
            lineDash: null
          }
        }
      },
      scale: [
        {
          dataKey: 'score',
          min: 0,
          max: 80
        }
      ],
      axisData: [
        { item: '引用', a: 70, b: 30, c: 40 },
        { item: '口碑', a: 60, b: 70, c: 40 },
        { item: '产量', a: 50, b: 60, c: 40 },
        { item: '贡献', a: 40, b: 50, c: 40 },
        { item: '热度', a: 60, b: 70, c: 40 },
        { item: '引用', a: 70, b: 50, c: 40 }
      ],
      radarData: [],

      market: [],
      infodetail: {},
      rongzilist: [],
      userdetail: {},
      lizhidetail: '',
      wapurl: ''
    }
  },
  computed: {
    ...mapState({
      nickname: state => state.user.nickname,
      welcome: state => state.user.welcome
    }),
    currentUser() {
      return {
        name: 'Serati Ma',
        avatar: 'https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png'
      }
    },
    userInfo() {
      return this.$store.getters.userInfo
    }
  },
  created() {
    this.user = this.userInfo
    this.avatar = this.userInfo.avatar
  },
  mounted() {
    this.getAgentInfo()
  },
  methods: {
    onCopy(text) {
      this.$copyText(text).then(() => {
        this.$message.success('复制成功')
      }, () => {
        this.$message.error('复制失败')
      })
    },
    getlizhi() {
      this.$http.post('/agent/getlizhi.do', {
      }).then(res => {
        if (res.code === 0) {
          this.userdetail = res.data
        }
      })
    },
    getAgentInfo() {
      this.$http.post('/agent/getAgentInfo.do', {
      }).then(res => {
        if (res.code === 0) {
          this.userdetail = res.data
          this.wapurl = res.data.wapurl
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
.dashboard-container {
  margin: -24px;
  padding: 24px;
  background: #f0f2f5;

  .dashboard-card {
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s;
    height: 100%;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .card-title {
      font-size: 16px;
      font-weight: 500;
      color: rgba(255, 255, 255, 0.95);
      margin-bottom: 12px;
    }

    .card-content {
      .today-value,
      .total-value {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.85);
        line-height: 22px;
      }
    }
  }

  // 成员数据卡片（第一列）
  .team-members-card,
  .first-gen-members-card,
  .second-gen-members-card,
  .third-gen-members-card,
  .fourth-gen-members-card,
  .fifth-gen-members-card {
    background: linear-gradient(135deg, #ffa726, #fb8c00);
  }

  // 活跃人数卡片（第二列）
  .team-active-users-card,
  .first-gen-active-users-card,
  .second-gen-active-users-card,
  .third-gen-active-users-card,
  .fourth-gen-active-users-card,
  .fifth-gen-active-users-card {
    background: linear-gradient(135deg, #29b6f6, #0288d1);
  }

  // 充值卡片（第三列）
  .team-recharge-card,
  .first-gen-recharge-card,
  .second-gen-recharge-card,
  .third-gen-recharge-card,
  .fourth-gen-recharge-card,
  .fifth-gen-recharge-card {
    background: linear-gradient(135deg, #66bb6a, #43a047);
  }

  // 提现卡片（第四列）
  .team-withdraw-card,
  .first-gen-withdraw-card,
  .second-gen-withdraw-card,
  .third-gen-withdraw-card,
  .fourth-gen-withdraw-card,
  .fifth-gen-withdraw-card {
    background: linear-gradient(135deg, #ef5350, #e53935);
  }

  // 利润卡片（第五列）
  .team-profit-card,
  .first-gen-profit-card,
  .second-gen-profit-card,
  .third-gen-profit-card,
  .fourth-gen-profit-card,
  .fifth-gen-profit-card {
    background: linear-gradient(135deg, #ab47bc, #8e24aa);
  }

  // 跟单人数卡片（第六列）
  .team-follows-card,
  .first-gen-follows-card,
  .second-gen-follows-card,
  .third-gen-follows-card,
  .fourth-gen-follows-card,
  .fifth-gen-follows-card {
    background: linear-gradient(135deg, #26a69a, #00897b);
  }

  // 智投标的收益卡片（第七列）
  .team-smart-invest-card,
  .first-gen-smart-invest-card,
  .second-gen-smart-invest-card,
  .third-gen-smart-invest-card,
  .fourth-gen-smart-invest-card,
  .fifth-gen-smart-invest-card {
    background: linear-gradient(135deg, #5c6bc0, #3949ab);
  }

  .everylist {
    padding: 16px 0;
    border-bottom: 1px solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }
  }

  // 响应式布局调整
  @media screen and (max-width: 1600px) {
    .dashboard-card {
      margin-bottom: 16px;
    }
  }

  @media screen and (max-width: 1200px) {
    .dashboard-card {
      .card-title {
        font-size: 14px;
      }
      .card-content {
        .today-value,
        .total-value {
          font-size: 12px;
        }
      }
    }
  }

  @media screen and (max-width: 768px) {
    .dashboard-card {
      margin-bottom: 12px;
    }
  }
}
</style>
