<template>
  <page-header-wrapper>
    <div class="dashboard-container">
      <!-- 团队数据 -->
      <a-row :gutter="[16, 16]">
        <a-col v-for="(item, index) in cardRows[0]" :key="index" :xs="24" :sm="12" :md="8" :lg="6" :xl="3">
          <a-card :class="['dashboard-card', getCardClass(item.type)]">
            <div class="card-title">{{ item.title }}</div>
            <div class="card-content">
              <div class="today-value">今日: {{ userdetail[`today${item.key}`] || 0 }}</div>
              <div class="total-value">总计: {{ userdetail[`total${item.key}`] || 0 }}</div>
            </div>
          </a-card>
        </a-col>
      </a-row>
      <!-- 一代数据 -->
      <a-row :gutter="[16, 16]" style="margin-top: 16px;">
        <a-col v-for="(item, index) in cardRows[1]" :key="index" :xs="24" :sm="12" :md="8" :lg="6" :xl="3">
          <a-card :class="['dashboard-card', getCardClass(item.type)]">
            <div class="card-title">{{ item.title }}</div>
            <div class="card-content">
              <div class="today-value">今日: {{ userdetail[`today${item.key}`] || 0 }}</div>
              <div class="total-value">总计: {{ userdetail[`total${item.key}`] || 0 }}</div>
            </div>
          </a-card>
        </a-col>
      </a-row>
      <!-- 二代数据 -->
      <a-row :gutter="[16, 16]" style="margin-top: 16px;">
        <a-col v-for="(item, index) in cardRows[2]" :key="index" :xs="24" :sm="12" :md="8" :lg="6" :xl="3">
          <a-card :class="['dashboard-card', getCardClass(item.type)]">
            <div class="card-title">{{ item.title }}</div>
            <div class="card-content">
              <div class="today-value">今日: {{ userdetail[`today${item.key}`] || 0 }}</div>
              <div class="total-value">总计: {{ userdetail[`total${item.key}`] || 0 }}</div>
            </div>
          </a-card>
        </a-col>
      </a-row>
      <!-- 三代数据 -->
      <a-row :gutter="[16, 16]" style="margin-top: 16px;">
        <a-col v-for="(item, index) in cardRows[3]" :key="index" :xs="24" :sm="12" :md="8" :lg="6" :xl="3">
          <a-card :class="['dashboard-card', getCardClass(item.type)]">
            <div class="card-title">{{ item.title }}</div>
            <div class="card-content">
              <div class="today-value">今日: {{ userdetail[`today${item.key}`] || 0 }}</div>
              <div class="total-value">总计: {{ userdetail[`total${item.key}`] || 0 }}</div>
            </div>
          </a-card>
        </a-col>
      </a-row>
      <!-- 四代数据 -->
      <a-row :gutter="[16, 16]" style="margin-top: 16px;">
        <a-col v-for="(item, index) in cardRows[4]" :key="index" :xs="24" :sm="12" :md="8" :lg="6" :xl="3">
          <a-card :class="['dashboard-card', getCardClass(item.type)]">
            <div class="card-title">{{ item.title }}</div>
            <div class="card-content">
              <div class="today-value">今日: {{ userdetail[`today${item.key}`] || 0 }}</div>
              <div class="total-value">总计: {{ userdetail[`total${item.key}`] || 0 }}</div>
            </div>
          </a-card>
        </a-col>
      </a-row>
      <!-- 五代数据 -->
      <a-row :gutter="[16, 16]" style="margin-top: 16px;">
        <a-col v-for="(item, index) in cardRows[5]" :key="index" :xs="24" :sm="12" :md="8" :lg="6" :xl="3">
          <a-card :class="['dashboard-card', getCardClass(item.type)]">
            <div class="card-title">{{ item.title }}</div>
            <div class="card-content">
              <div class="today-value">今日: {{ userdetail[`today${item.key}`] || 0 }}</div>
              <div class="total-value">总计: {{ userdetail[`total${item.key}`] || 0 }}</div>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>
  </page-header-wrapper>
</template>

<script>
import { timeFix } from '@/utils/util'
import { mapState } from 'vuex'
import { PageHeaderWrapper } from '@ant-design-vue/pro-layout'
import { agentgetAgentInfo, getAgentCountData } from '@/api/home'
export default {
  name: 'Workplace',
  components: {
    PageHeaderWrapper
  },
  data() {
    return {
      // 卡片类型定义
      cardTypes: [
        { type: 'member', title: '成员数', todayKey: 'memberNumToday', totalKey: 'memberNum', isAmount: false },
        { type: 'active', title: '活跃人数', todayKey: 'activeNumToday', totalKey: 'activeNum', isAmount: false },
        { type: 'recharge', title: '充值金额', todayKey: 'rechargeAmountToday', totalKey: 'rechargeAmount', isAmount: true },
        { type: 'withdraw', title: '提现金额', todayKey: 'withdrawAmountToday', totalKey: 'withdrawAmount', isAmount: true },
        { type: 'profit', title: '利润金额', todayKey: 'incomeAmountToday', totalKey: 'incomeAmount', isAmount: true },
        { type: 'follow', title: '跟单人数', todayKey: 'followNumToday', totalKey: 'followNum', isAmount: false },
        { type: 'smart-invest', title: '跟单收益', todayKey: 'followIncomeToday', totalKey: 'followIncome', isAmount: true }
      ],
      // 各级数据
      levelDataList: [],
      pieScale: null,
      pieData: null,
      sourceData: null,
      pieStyle: {
        stroke: '#fff',
        lineWidth: 1
      },
      timeFix: timeFix(),
      avatar: '',
      user: {},

      projects: [],
      loading: true,
      radarLoading: true,
      activities: [],
      teams: [],

      // data
      axis1Opts: {
        dataKey: 'item',
        line: null,
        tickLine: null,
        grid: {
          lineStyle: {
            lineDash: null
          },
          hideFirstLine: false
        }
      },
      axis2Opts: {
        dataKey: 'score',
        line: null,
        tickLine: null,
        grid: {
          type: 'polygon',
          lineStyle: {
            lineDash: null
          }
        }
      },
      scale: [
        {
          dataKey: 'score',
          min: 0,
          max: 80
        }
      ],
      axisData: [
        { item: '引用', a: 70, b: 30, c: 40 },
        { item: '口碑', a: 60, b: 70, c: 40 },
        { item: '产量', a: 50, b: 60, c: 40 },
        { item: '贡献', a: 40, b: 50, c: 40 },
        { item: '热度', a: 60, b: 70, c: 40 },
        { item: '引用', a: 70, b: 50, c: 40 }
      ],
      radarData: [],

      market: [],
      infodetail: {},
      rongzilist: [],
      userdetail: {},
      lizhidetail: '',
      wapurl: ''
    }
  },
  computed: {
    ...mapState({
      nickname: state => state.user.nickname,
      welcome: state => state.user.welcome
    }),
    // 生成卡片行数据
    cardRows() {
      const rows = [];
      for (let i = 0; i < 6; i++) {
        rows.push(this.cardTypes);
      }
      console.log('rows', rows);
      return rows;
    },
    currentUser() {
      return {
        name: 'Serati Ma',
        avatar: 'https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png'
      }
    },
    userInfo() {
      return this.$store.getters.userInfo
    }
  },

  created() {
    this.user = this.userInfo
    this.avatar = this.userInfo.avatar
    // this.getCountData()
  },
  mounted() {
    this.getAgentInfo()
  },

  methods: {
    // 根据卡片类型返回对应的CSS类名
    getCardClass(type) {
      const classMap = {
        'member': 'card-member',           // 第一列 - 成员数据 (橙色)
        'active': 'card-active',           // 第二列 - 活跃人数 (蓝色)
        'recharge': 'card-recharge',       // 第三列 - 充值 (绿色)
        'withdraw': 'card-withdraw',       // 第四列 - 提现 (红色)
        'profit': 'card-profit',           // 第五列 - 利润 (紫色)
        'follow': 'card-follow',           // 第六列 - 跟单人数 (青色)
        'smart-invest': 'card-smart-invest' // 第七列 - 智投标的收益 (靛蓝色)
      }
      return classMap[type] || 'card-default'
    },
    // 格式化数值显示
    formatValue(value, isAmount = false) {
      if (value === null || value === undefined) return '0'
      if (isAmount) {
        return Number(value).toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })
      }
      return Number(value).toLocaleString('zh-CN')
    },
    onCopy(text) {
      this.$copyText(text).then(() => {
        this.$message.success('复制成功')
      }, () => {
        this.$message.error('复制失败')
      })
    },
    getlizhi() {
      // 模拟励志数据
      this.lizhidetail = '成功不是终点，失败不是末日，继续前进的勇气才最可贵。'
    },
    getAgentInfo() {
      getAgentCountData().then(res => {
        
        if (res.status === 0) {
          // 转换数据格式
          this.levelDataList = [
            { title: '团队总计', data: res.data.total },
            { title: '一代数据', data: res.data.lv1 },
            { title: '二代数据', data: res.data.lv2 },
            { title: '三代数据', data: res.data.lv3 },
            { title: '四代数据', data: res.data.lv4 },
            { title: '五代数据', data: res.data.lv5 },
            { title: '六代数据', data: res.data.lv6 }
          ]
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
.dashboard-container {
  margin: -24px;
  padding: 24px;
  background: #f0f2f5;

  .dashboard-card {
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s;
    height: 100%;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    font-weight: bold;

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .card-title {
      font-size: 16px;
      color: rgba(255, 255, 255, 0.95);
      margin-bottom: 12px;
    }

    .card-content {

      .today-value,
      .total-value {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.85);
        line-height: 22px;
        white-space: nowrap;
      }
    }
  }

  // 第一列 - 成员数据卡片（橙色渐变）
  .card-member {
    background: linear-gradient(135deg, #ffa726, #fb8c00);
  }

  // 第二列 - 活跃人数卡片（蓝色渐变）
  .card-active {
    background: linear-gradient(135deg, #29b6f6, #0288d1);
  }

  // 第三列 - 充值卡片（绿色渐变）
  .card-recharge {
    background: linear-gradient(135deg, #66bb6a, #43a047);
  }

  // 第四列 - 提现卡片（红色渐变）
  .card-withdraw {
    background: linear-gradient(135deg, #ef5350, #e53935);
  }

  // 第五列 - 利润卡片（紫色渐变）
  .card-profit {
    background: linear-gradient(135deg, #ab47bc, #8e24aa);
  }

  // 第六列 - 跟单人数卡片（青色渐变）
  .card-follow {
    background: linear-gradient(135deg, #26a69a, #00897b);
  }

  // 第七列 - 智投标的收益卡片（靛蓝色渐变）
  .card-smart-invest {
    background: linear-gradient(135deg, #5c6bc0, #3949ab);
  }

  // 默认卡片样式
  .card-default {
    background: linear-gradient(135deg, #78909c, #546e7a);
  }

  .everylist {
    padding: 16px 0;
    border-bottom: 1px solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }
  }

  // 响应式布局调整
  @media screen and (max-width: 1600px) {
    .dashboard-card {
      margin-bottom: 16px;
    }
  }

  @media screen and (max-width: 1200px) {
    .dashboard-card {
      .card-title {
        font-size: 14px;
      }

      .card-content {

        .today-value,
        .total-value {
          font-size: 12px;
        }
      }
    }
  }

  @media screen and (max-width: 768px) {
    .dashboard-card {
      margin-bottom: 12px;
    }
  }
}
</style>
