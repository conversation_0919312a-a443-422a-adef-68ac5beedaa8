<template>
  <page-header-wrapper>
    <div class="dashboard-container">
      <!-- 数据卡片 -->
      <div v-for="(levelData, levelIndex) in levelDataList" :key="levelIndex" class="level-section">
        <h3 class="level-title">{{ levelData.title }}</h3>
        <a-row :gutter="[16, 16]">
          <a-col :xs="24" :sm="12" :md="8" :lg="6" :xl="3">
            <a-card :class="['dashboard-card', 'card-member']">
              <div class="card-title">成员数</div>
              <div class="card-content">
                <div class="today-value">今日: {{ formatValue(levelData.data.memberNumToday, false) }}</div>
                <div class="total-value">总计: {{ formatValue(levelData.data.memberNum, false) }}</div>
              </div>
            </a-card>
          </a-col>
          <a-col :xs="24" :sm="12" :md="8" :lg="6" :xl="3">
            <a-card :class="['dashboard-card', 'card-active']">
              <div class="card-title">活跃人数</div>
              <div class="card-content">
                <div class="today-value">今日: {{ formatValue(levelData.data.activeNumToday, false) }}</div>
                <div class="total-value">总计: {{ formatValue(levelData.data.activeNum, false) }}</div>
              </div>
            </a-card>
          </a-col>
          <a-col :xs="24" :sm="12" :md="8" :lg="6" :xl="3">
            <a-card :class="['dashboard-card', 'card-recharge']">
              <div class="card-title">充值金额</div>
              <div class="card-content">
                <div class="today-value">今日: {{ formatValue(levelData.data.rechargeAmountToday, true) }}</div>
                <div class="total-value">总计: {{ formatValue(levelData.data.rechargeAmount, true) }}</div>
              </div>
            </a-card>
          </a-col>
          <a-col :xs="24" :sm="12" :md="8" :lg="6" :xl="3">
            <a-card :class="['dashboard-card', 'card-withdraw']">
              <div class="card-title">提现金额</div>
              <div class="card-content">
                <div class="today-value">今日: {{ formatValue(levelData.data.withdrawAmountToday, true) }}</div>
                <div class="total-value">总计: {{ formatValue(levelData.data.withdrawAmount, true) }}</div>
              </div>
            </a-card>
          </a-col>
          <a-col :xs="24" :sm="12" :md="8" :lg="6" :xl="3">
            <a-card :class="['dashboard-card', 'card-profit']">
              <div class="card-title">利润金额</div>
              <div class="card-content">
                <div class="today-value">今日: {{ formatValue(levelData.data.incomeAmountToday, true) }}</div>
                <div class="total-value">总计: {{ formatValue(levelData.data.incomeAmount, true) }}</div>
              </div>
            </a-card>
          </a-col>
          <a-col :xs="24" :sm="12" :md="8" :lg="6" :xl="3">
            <a-card :class="['dashboard-card', 'card-follow']">
              <div class="card-title">跟单人数</div>
              <div class="card-content">
                <div class="today-value">今日: {{ formatValue(levelData.data.followNumToday, false) }}</div>
                <div class="total-value">总计: {{ formatValue(levelData.data.followNum, false) }}</div>
              </div>
            </a-card>
          </a-col>
          <a-col :xs="24" :sm="12" :md="8" :lg="6" :xl="3">
            <a-card :class="['dashboard-card', 'card-smart-invest']">
              <div class="card-title">跟单收益</div>
              <div class="card-content">
                <div class="today-value">今日: {{ formatValue(levelData.data.followIncomeToday, true) }}</div>
                <div class="total-value">总计: {{ formatValue(levelData.data.followIncome, true) }}</div>
              </div>
            </a-card>
          </a-col>
        </a-row>
      </div>

      <!-- 代理信息卡片 -->
      <a-card :bordered="false" style="margin-top: 24px;" title="代理信息">
        <div class="agent-info">
          <p>代理代码：A001</p>
          <p>代理名称：代理商001</p>
          <p>账号状态：正常</p>
        </div>
      </a-card>
    </div>
  </page-header-wrapper>
</template>

<script>
import { PageHeaderWrapper } from '@ant-design-vue/pro-layout'
import { getAgentCountData } from '@/api/home'

export default {
  name: 'Workplace',
  components: {
    PageHeaderWrapper
  },
  data() {
    return {
      // 各级数据
      levelDataList: []
    }
  },
  mounted() {
    this.getAgentInfo()
  },
  methods: {
    // 格式化数值显示
    formatValue(value, isAmount = false) {
      if (value === null || value === undefined) return '0'
      if (isAmount) {
        return Number(value).toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })
      }
      return Number(value).toLocaleString('zh-CN')
    },
    // 获取代理统计数据
    getAgentInfo() {
      getAgentCountData().then(res => {
        if (res.status === 0) {
          // 转换数据格式
          this.levelDataList = [
            { title: '团队总计', data: res.data.total },
            { title: '一代数据', data: res.data.lv1 },
            { title: '二代数据', data: res.data.lv2 },
            { title: '三代数据', data: res.data.lv3 },
            { title: '四代数据', data: res.data.lv4 },
            { title: '五代数据', data: res.data.lv5 },
            { title: '六代数据', data: res.data.lv6 }
          ]
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
.dashboard-container {
  margin: -24px;
  padding: 24px;
  background: #f0f2f5;

  .level-section {
    margin-bottom: 32px;

    .level-title {
      font-size: 18px;
      font-weight: 600;
      color: #262626;
      margin-bottom: 16px;
      padding-left: 8px;
      border-left: 4px solid #1890ff;
    }
  }

  .dashboard-card {
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s;
    height: 100%;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    font-weight: bold;

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .card-title {
      font-size: 16px;
      color: rgba(255, 255, 255, 0.95);
      margin-bottom: 12px;
    }

    .card-content {
      .today-value,
      .total-value {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.85);
        line-height: 22px;
        white-space: nowrap;
      }
    }
  }

  // 第一列 - 成员数据卡片（橙色渐变）
  .card-member {
    background: linear-gradient(135deg, #ffa726, #fb8c00);
  }

  // 第二列 - 活跃人数卡片（蓝色渐变）
  .card-active {
    background: linear-gradient(135deg, #29b6f6, #0288d1);
  }

  // 第三列 - 充值卡片（绿色渐变）
  .card-recharge {
    background: linear-gradient(135deg, #66bb6a, #43a047);
  }

  // 第四列 - 提现卡片（红色渐变）
  .card-withdraw {
    background: linear-gradient(135deg, #ef5350, #e53935);
  }

  // 第五列 - 利润卡片（紫色渐变）
  .card-profit {
    background: linear-gradient(135deg, #ab47bc, #8e24aa);
  }

  // 第六列 - 跟单人数卡片（青色渐变）
  .card-follow {
    background: linear-gradient(135deg, #26a69a, #00897b);
  }

  // 第七列 - 智投标的收益卡片（靛蓝色渐变）
  .card-smart-invest {
    background: linear-gradient(135deg, #5c6bc0, #3949ab);
  }

  .agent-info {
    p {
      margin-bottom: 8px;
      font-size: 14px;
      color: #666;
    }
  }
}
</style>
