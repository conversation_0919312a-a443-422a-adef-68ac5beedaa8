// 配置项 只能是js，因为要给不同编译环境使用
const envDev = require('./env/dev.js');
const envTest = require('./env/test.js');
const envProd = require('./env/prod.js');

const compileEnv = process.env.APP_ENV || 'dev';

// 环境配置字典
const config = {
  // 开发环境配置
  dev: envDev,
  // 测试环境配置
  test: envTest,
  // 生产环境配置
  prod: envProd
};

// 最终应用的环境 - 编译环境
let CONFIG = config[compileEnv];

// 公共配置
const configCommon = {
  // 默认分页大小
  pageSize: 10,

  // 是否使用静态数据 - 防止后台发版、无法访问
  isUseStaticData: false,

  // 静态资源地址
  staticPath: CONFIG.APP_ENV === 'prod' ? './static' : '/static'
};

// 合并公共配置
CONFIG = Object.assign({}, configCommon, CONFIG);

if(CONFIG.isUseStaticData){
  console.error(`请注意：当前部分接口使用本地数据`);
}

// mock接口地址
const mockApiBase = `${CONFIG.staticPath}/_mocks`;

module.exports = CONFIG;
module.exports.compileEnv = compileEnv;
module.exports.mockApiBase = mockApiBase;
module.exports.APP_ENV = process.env.APP_ENV || 'dev';
module.exports.VUE_APP_PREVIEW = process.env.VUE_APP_PREVIEW || 'false';
