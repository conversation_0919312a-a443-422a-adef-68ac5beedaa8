<template>
  <div>
    <a-modal 
      title="提现审核" 
      :width="600" 
      :visible="visible" 
      :confirmLoading="auditLoading"
      @ok="handleSubmit" 
      @cancel="handleCancel"
    >
      <!-- 提现信息展示 -->
      <a-descriptions bordered :column="2" style="margin-bottom: 20px;">
        <a-descriptions-item label="用户信息">
          {{ withdrawInfo.nickName }}（{{ withdrawInfo.userId }}）
        </a-descriptions-item>
        <a-descriptions-item label="订单号">
          {{ withdrawInfo.orderSn }}
        </a-descriptions-item>
        <a-descriptions-item label="提现金额">
          {{ withdrawInfo.withAmt }}
        </a-descriptions-item>
        <a-descriptions-item label="手续费">
          {{ withdrawInfo.withFee }}
        </a-descriptions-item>
        <a-descriptions-item label="实际到账">
          {{ withdrawInfo.withAmt - withdrawInfo.withFee }}
        </a-descriptions-item>
        <a-descriptions-item label="提现银行">
          {{ withdrawInfo.bankName }}
        </a-descriptions-item>
        <a-descriptions-item label="银行卡号" :span="2">
          {{ withdrawInfo.bankNo }}
        </a-descriptions-item>
        <a-descriptions-item label="申请时间" :span="2">
          {{ formatTime(withdrawInfo.applyTime) }}
        </a-descriptions-item>
      </a-descriptions>

      <!-- 审核表单 -->
      <a-form :form="auditForm" layout="vertical">
        <a-form-item label="审核结果">
          <a-radio-group 
            v-decorator="['state', { rules: [{ required: true, message: '请选择审核结果' }] }]"
            @change="handleAuditResultChange"
          >
            <a-radio :value="1">通过</a-radio>
            <a-radio :value="2">拒绝</a-radio>
          </a-radio-group>
        </a-form-item>
        
        <a-form-item 
          label="审核备注" 
          v-if="showRemarkField"
        >
          <a-textarea 
            v-decorator="['authMsg', { rules: [{ required: showRemarkField, message: '请输入审核备注' }] }]"
            placeholder="请输入审核备注"
            :rows="4"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script>
import { withdrawcheck } from '@/api/capital'
import moment from 'moment'

export default {
  name: 'WithdrawAuditDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    withdrawInfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      auditForm: this.$form.createForm(this),
      auditLoading: false,
      showRemarkField: false
    }
  },
  watch: {
    visible(val) {
      if (val) {
        // 当对话框显示时，重置表单
        this.$nextTick(() => {
          this.auditForm.resetFields()
          this.showRemarkField = false
        })
      }
    }
  },
  methods: {
    // 处理审核结果变化
    handleAuditResultChange(e) {
      this.showRemarkField = e.target.value === 2 // 拒绝时显示备注字段
    },
    
    // 处理取消
    handleCancel() {
      this.$emit('cancel')
    },
    
    // 处理提交
    handleSubmit() {
      this.auditForm.validateFields((err, values) => {
        if (!err) {
          this.auditLoading = true
          
          const params = {
            withId: this.withdrawInfo.id,
            state: values.state,
            authMsg: values.authMsg || ''
          }
          
          withdrawcheck(params).then(res => {
            if (res.status === 0) {
              this.$message.success('审核成功')
              this.$emit('success')
            } else {
              this.$message.error(res.msg || '审核失败')
            }
            this.auditLoading = false
          }).catch(err => {
            console.error('Audit error:', err)
            this.$message.error('审核失败，请稍后再试')
            this.auditLoading = false
          })
        }
      })
    },
    
    // 格式化时间
    formatTime(timestamp) {
      return timestamp ? moment(timestamp).format('YYYY-MM-DD HH:mm:ss') : '--'
    }
  }
}
</script>

<style lang="less" scoped>
.ant-descriptions {
  background: #fafafa;
}
</style>
