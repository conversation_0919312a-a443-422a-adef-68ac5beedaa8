<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="商户名称">
                <a-input v-model="queryParam.name" style="width: 100%" placeholder="请输入商户名称" />
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="商户状态">
                <a-select v-model="queryParam.status" placeholder="请选择商户状态" allowClear>
                  <a-select-option :value="1">正常</a-select-option>
                  <a-select-option :value="0">冻结</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item>
                <span class="table-page-search-submitButtons">
                  <a-button icon="redo" @click="resetQuery">重置</a-button>
                  <a-button type="primary" icon="search" style="margin-left: 8px" @click="getList">查询</a-button>
                </span>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="24">
              <a-form-item>
                <a-button type="primary" icon="plus" @click="handleAdd">添加商户</a-button>
                <a-button type="primary" icon="reload" style="margin-left: 8px" @click="getList">刷新数据</a-button>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>

    <a-card :bordered="false" style="margin-top: 24px">
      <a-table :columns="columns" :rowKey="record => record.id" :dataSource="dataSource" :pagination="pagination"
        :loading="loading" @change="handleTableChange" :scroll="{ x: 1200 }" bordered>
        <template slot="avatar" slot-scope="text">
          <img v-if="text" :src="text" alt="商户头像"
            style="width: 60px; height: 60px; object-fit: cover; border-radius: 4px;" />
          <span v-else>无头像</span>
        </template>

        <template slot="status" slot-scope="text">
          <a-badge :status="text === 1 ? 'success' : 'error'" :text="text === 1 ? '正常' : '冻结'" />
        </template>

        <template slot="isRechange" slot-scope="text">
          <a-tag :color="text === 1 ? 'green' : 'red'">
            {{ text === 1 ? '显示' : '不显示' }}
          </a-tag>
        </template>

        <template slot="isWithdraw" slot-scope="text">
          <a-tag :color="text === 1 ? 'green' : 'red'">
            {{ text === 1 ? '显示' : '不显示' }}
          </a-tag>
        </template>

        <template slot="action" slot-scope="text, record">
          <a @click="handleEdit(record)">编辑</a>
          <a-divider type="vertical" />
          <a-popconfirm title="确定要删除这个商户吗？" ok-text="确定" cancel-text="取消" @confirm="handleDelete(record)">
            <a>删除</a>
          </a-popconfirm>
        </template>
      </a-table>
    </a-card>

    <!-- 添加/编辑商户对话框 -->
    <merchant-pay-dialog :visible="dialogVisible" v-if="dialogVisible" :loading="confirmLoading" :model="currentItem"
      :is-edit="isEdit" @cancel="handleCancel" @submit="handleSubmit" />
  </page-header-wrapper>
</template>

<script>
import { merchantPayList, merchantPayAdd, merchantPayUpdate, merchantPayDelete } from '@/api/merchantPay'
import MerchantPayDialog from './components/MerchantPayDialog'
import moment from 'moment'

export default {
  name: 'MerchantPay',
  components: {
    MerchantPayDialog
  },
  data() {
    return {
      // 查询参数
      queryParam: {
        pageNum: 1,
        pageSize: 10,
        name: '',
        status: undefined
      },
      // 表格列定义
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          width: 80,
          fixed: 'left'
        },
        {
          title: '商户名称',
          dataIndex: 'name',
          width: 150,
          ellipsis: true
        },
        {
          title: '商户头像',
          dataIndex: 'avatar',
          scopedSlots: { customRender: 'avatar' },
          width: 100
        },
        {
          title: '充值区间',
          dataIndex: 'rechargeRange',
          width: 120
        },
        {
          title: '提现区间',
          dataIndex: 'withdrawRange',
          width: 120
        },
        {
          title: '币种',
          dataIndex: 'currency',
          width: 80
        },
        {
          title: '汇率',
          dataIndex: 'rate',
          width: 100
        },
        {
          title: '支付代码',
          dataIndex: 'code',
          width: 120
        },
        {
          title: '状态',
          dataIndex: 'status',
          scopedSlots: { customRender: 'status' },
          width: 100
        },
        {
          title: '充值显示',
          dataIndex: 'isRechange',
          scopedSlots: { customRender: 'isRechange' },
          width: 100
        },
        {
          title: '提现显示',
          dataIndex: 'isWithdraw',
          scopedSlots: { customRender: 'isWithdraw' },
          width: 100
        },
        {
          title: '排序',
          dataIndex: 'sort',
          width: 80
        },
        {
          title: '创建时间',
          dataIndex: 'createTime',
          width: 180,
          customRender: (text) => {
            return text ? moment(text).format('YYYY-MM-DD HH:mm:ss') : '-'
          }
        },
        {
          title: '操作',
          dataIndex: 'action',
          scopedSlots: { customRender: 'action' },
          width: 150,
          fixed: 'right'
        }
      ],
      // 表格数据
      dataSource: [],
      // 分页配置
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: total => `共 ${total} 条记录`
      },
      // 加载状态
      loading: false,
      // 对话框可见性
      dialogVisible: false,
      // 确认加载状态
      confirmLoading: false,
      // 当前编辑的项
      currentItem: {},
      // 是否为编辑模式
      isEdit: false
    }
  },
  created() {
    this.getList()
  },
  methods: {
    // 获取商户列表
    getList() {
      this.loading = true
      const params = {
        ...this.queryParam,
        pageNum: this.pagination.current,
        pageSize: this.pagination.pageSize
      }

      merchantPayList(params).then(res => {
        if (res.status === 0) {
          this.dataSource = res.data.list
          this.pagination.total = res.data.total
        } else {
          this.$message.error(res.msg || '获取商户列表失败')
        }
        this.loading = false
      }).catch(err => {
        console.error('获取商户列表失败:', err)
        this.$message.error('获取商户列表失败，请稍后重试')
        this.loading = false
      })
    },

    // 重置查询条件
    resetQuery() {
      this.queryParam = {
        pageNum: 1,
        pageSize: 10,
        name: '',
        status: undefined
      }
      this.pagination.current = 1
      this.getList()
    },

    // 处理表格变化（分页、排序等）
    handleTableChange(pagination) {
      this.pagination.current = pagination.current
      this.pagination.pageSize = pagination.pageSize
      this.getList()
    },

    // 处理添加商户
    handleAdd() {
      this.isEdit = false
      this.currentItem = {}
      this.dialogVisible = true
    },

    // 处理编辑商户
    handleEdit(record) {
      this.isEdit = true
      this.currentItem = { ...record }
      this.dialogVisible = true
    },

    // 处理删除商户
    handleDelete(record) {
      this.loading = true
      merchantPayDelete({ id: record.id }).then(res => {
        if (res.status === 0) {
          this.$message.success('删除商户成功')
          this.getList()
        } else {
          this.$message.error(res.msg || '删除商户失败')
        }
        this.loading = false
      }).catch(err => {
        console.error('删除商户失败:', err)
        this.$message.error('删除商户失败，请稍后重试')
        this.loading = false
      })
    },

    // 处理对话框取消
    handleCancel() {
      this.dialogVisible = false
    },

    // 处理表单提交
    handleSubmit(values) {
      this.confirmLoading = true

      const apiMethod = this.isEdit ? merchantPayUpdate : merchantPayAdd
      const successMsg = this.isEdit ? '更新商户成功' : '添加商户成功'
      const errorMsg = this.isEdit ? '更新商户失败' : '添加商户失败'

      apiMethod(values).then(res => {
        if (res.status === 0) {
          this.$message.success(successMsg)
          this.dialogVisible = false
          this.getList()
        } else {
          this.$message.error(res.msg || errorMsg)
        }
        this.confirmLoading = false
      }).catch(err => {
        console.error(`${errorMsg}:`, err)
        this.$message.error(`${errorMsg}，请稍后重试`)
        this.confirmLoading = false
      })
    }
  }
}
</script>

<style lang="less" scoped>
.table-page-search-submitButtons {
  display: flex;
  justify-content: flex-start;
  align-items: center;

  button {
    margin-right: 8px;
  }
}
</style>
