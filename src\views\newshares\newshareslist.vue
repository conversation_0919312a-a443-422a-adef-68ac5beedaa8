<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">

            <a-col :md="12" :lg="6" :sm="24">
              <a-form-item label="显示状态">
                <a-select v-model="queryParam.zt" placeholder="请选择显示状态">
                  <a-select-option :value="0">隐藏</a-select-option>
                  <a-select-option :value="1">显示</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="12" :lg="6" :sm="24">
              <a-form-item label="新股代码">
                <a-input v-model="queryParam.code" style="width: 100%" placeholder="请输入新股代码" />
              </a-form-item>
            </a-col>
            <a-col :md="12" :lg="6" :sm="24">
              <a-form-item label="新股名称">
                <a-input v-model="queryParam.name" style="width: 100%" placeholder="请输入新股名称" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="48">


            <a-col :md="12" :lg="6" :sm="24">
              <a-form-item>
                <span class="table-page-search-submitButtons">
                  <a-button @click="getqueryParam" icon="redo">
                    重置</a-button>
                  <a-button type="primary" icon="search" style="margin-left: 8px"
                    @click="queryParam.pageNum = 1, pagination.current = page, getlist()">查询
                  </a-button>
                  <!-- <a-button type="primary" icon="plus" style="margin-left: 8px"
                    @click="addUserdialog = true, currentdetail = ''">
                    添加新股</a-button> -->
                </span>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>

    <a-card :bordered="false">
      <a-table bordered :loading="loading" :pagination="pagination" :columns="columns" :data-source="datalist"
        :scroll="{ x: 1500 }" rowKey="newlistId">
        <span slot="name" slot-scope="text,record">
          <template>
            <div>
              <span style="margin-right:10px">{{ record.name }}</span>
              <a-tag color="green">{{ record.code }}
              </a-tag>
            </div>
          </template>
        </span>
        <span slot="zt" slot-scope="text,record">
          <template>
            <div>
              <a-tag :color="record.zt == 0 ? 'red' : record.zt == 1 ? 'green' : ''">
                {{ record.zt == 0 ? '隐藏' : '显示' }}
              </a-tag>
            </div>
          </template>
        </span> <span slot="stockType" slot-scope="text,record">
          <template>
            <div>
              <a-tag
                :color="record.stockType == 'sh' ? 'purple' : record.stockType == 'sz' ? 'blue' : record.stockType == 'hk' ? 'green' : 'orange'">
                {{ record.stockType == 'sh' ? '上海' : record.stockType == 'sz' ? '深圳' : record.stockType == 'hk' ? '香港' :
                  '美国' }}
              </a-tag>
            </div>
          </template>
        </span>
        <span slot="leixing" slot-scope="text,record">
          <template>
            <div>
              <a-tag :color="getleixing(record.listDate) == '新股抢筹' ? 'orange' : 'green'">
                {{ getleixing(record.listDate) }}
              </a-tag>
            </div>
          </template>
        </span>

        <template slot="action" slot-scope="text,record">
          <a @click="showDetail(record)">详情</a>
        </template>
      </a-table>
    </a-card>

    <!-- 详情弹窗 -->
    <a-modal title="新股详情" v-if="detailVisible" :width="800" :visible="detailVisible" :footer="null"
      @cancel="handleDetailCancel">
      <a-descriptions bordered :column="2">
        <a-descriptions-item label="新股名称">{{ detailData.name }}</a-descriptions-item>
        <a-descriptions-item label="新股代码">
          <a-tag color="green">{{ detailData.code }}</a-tag>
        </a-descriptions-item>

        <a-descriptions-item label="最大购买数量">{{ detailData.orderNumber }}/万股</a-descriptions-item>
        <a-descriptions-item label="价格">{{ detailData.price }}</a-descriptions-item>

        <a-descriptions-item label="市盈率">{{ detailData.pe }}</a-descriptions-item>
        <a-descriptions-item label="显示状态">
          <a-tag :color="detailData.zt == 0 ? 'red' : 'green'">
            {{ detailData.zt == 0 ? '隐藏' : '显示' }}
          </a-tag>
        </a-descriptions-item>

        <a-descriptions-item label="股票类型">
          <a-tag
            :color="detailData.stockType == 'sh' ? 'purple' : detailData.stockType == 'sz' ? 'blue' : detailData.stockType == 'hk' ? 'green' : 'orange'">
            {{ detailData.stockType == 'sh' ? '上海' : detailData.stockType == 'sz' ? '深圳' : detailData.stockType == 'hk'
              ?
              '香港' : '美国' }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="类型">
          <a-tag :color="getleixing(detailData.listDate) == '新股抢筹' ? 'orange' : 'green'">
            {{ getleixing(detailData.listDate) }}
          </a-tag>
        </a-descriptions-item>

        <a-descriptions-item label="申购时间">
          {{ detailData.subscribeTime }}
        </a-descriptions-item>
        <a-descriptions-item label="中签时间">
          {{ detailData.subscriptionTime }}
        </a-descriptions-item>

        <a-descriptions-item label="缴费时间">
          {{ detailData.payTime }}
        </a-descriptions-item>
        <a-descriptions-item label="上市时间">
          {{ detailData.listDate }}
        </a-descriptions-item>
      </a-descriptions>
    </a-modal>

    <a-modal :title="currentdetail ? '修改新股' : '添加新股'" :width="700" okText="确定" cancelText="取消" v-if="addUserdialog"
      :visible="addUserdialog" :confirmLoading="addUserDialogloading" @ok="OkaddUserdialog"
      @cancel="CanceladdUserdialog">
      <a-form :form="addUserform" ref="addUserform">
        <a-row :gutter="48">
          <a-col :md="12" :lg="12" :sm="12">
            <a-form-item label="新股名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input placeholder="请输入新股名称"
                v-decorator="['name', { rules: [{ required: true, message: '请输入新股名称', }] }]" />
            </a-form-item>
          </a-col>
          <a-col :md="12" :lg="12" :sm="12">
            <a-form-item label="新股代码" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input placeholder="请输入新股代码"
                v-decorator="['code', { rules: [{ required: true, message: '请输入新股代码', }] }]" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="48">
          <a-col :md="12" :lg="12" :sm="12">
            <a-form-item label="新股价格" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input-number placeholder="请输入新股价格" :min="0" :step="0.01" :precision="2" style="width: 100%"
                v-decorator="['price', { rules: [{ required: true, message: '请输入新股价格', }] }]" />
            </a-form-item>
          </a-col>
          <a-col :md="12" :lg="12" :sm="12">
            <a-form-item label="最大购买数量" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input-number placeholder="请输入发行数量" :min="0" :step="1000" style="width: 100%"
                v-decorator="['orderNumber', { rules: [{ required: true, message: '请输入发行数量', }] }]" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="48">
          <a-col :md="12" :lg="12" :sm="12">
            <a-form-item label="股票类型" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-select placeholder="请选择股票类型"
                v-decorator="['stockType', { rules: [{ required: true, message: '请选择股票类型', }] }]">
                <a-select-option value="sh">上海</a-select-option>
                <a-select-option value="sz">深圳</a-select-option>
                <a-select-option value="hk">香港</a-select-option>
                <a-select-option value="us">美国</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="12" :lg="12" :sm="12">
            <a-form-item label="显示状态" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-select placeholder="请选择显示状态"
                v-decorator="['zt', { rules: [{ required: true, message: '请选择显示状态', }] }]">
                <a-select-option :value="0">隐藏</a-select-option>
                <a-select-option :value="1">显示</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="48">

          <a-col :md="12" :lg="12" :sm="12">
            <a-form-item label="申购时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-date-picker show-time style="width:100%" @change="getsubscribeTime"
                v-decorator="['subscribeTime', { rules: [{ required: true, message: '请填写申购时间', }] }]"
                format="YYYY-MM-DD HH:mm:ss">
              </a-date-picker>
            </a-form-item> </a-col>
          <a-col :md="12" :lg="12" :sm="12">
            <a-form-item label="中签时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-date-picker show-time style="width:100%" @change="getsubscriptionTime"
                v-decorator="['subscriptionTime', { rules: [{ required: true, message: '请填写中签时间', }] }]"
                format="YYYY-MM-DD HH:mm:ss">
              </a-date-picker>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="48">
          <a-col :md="12" :lg="12" :sm="12">
            <a-form-item label="上市时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-date-picker show-time style="width:100%" @change="getlistDate"
                v-decorator="['listDate', { rules: [{ required: true, message: '请填写上市时间', }] }]"
                format="YYYY-MM-DD HH:mm:ss">
              </a-date-picker>
            </a-form-item>
          </a-col>
          <a-col :md="12" :lg="12" :sm="12">
            <a-form-item label="缴费时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-date-picker show-time style="width:100%" @change="getpayTime"
                v-decorator="['payTime', { rules: [{ required: true, message: '请填写缴费时间', }] }]"
                format="YYYY-MM-DD HH:mm:ss">
              </a-date-picker>
            </a-form-item>
          </a-col>
          <a-col :md="12" :lg="12" :sm="12">
            <a-form-item label="市盈率" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input-number placeholder="请输入市盈率" :min="0" :step="0.1" :precision="1" style="width: 100%"
                v-decorator="['pe', { rules: [{ required: true, message: '请输入市盈率', }] }]" />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-modal>
  </page-header-wrapper>
</template>
<script>
import { subscribeList, subscribeAdd, subscribeUpdate, subscribeDel } from '@/api/newshares'
import moment from 'moment'
import pick from 'lodash.pick'

export default {
  name: 'shares',
  data() {
    return {
      // 详情弹窗
      detailVisible: false,
      detailData: {},
      columns: [
        {
          title: '新股名称 / 新股代码',
          dataIndex: 'name',
          align: 'center',
          fixed: 'left',
          width: 200,
          scopedSlots: { customRender: 'name' }
        },
        {
          title: '最大购买数量',
          dataIndex: 'orderNumber',
          width: 120,
          align: 'center',
          customRender: (text, row, index) => {
            return text + '/万股'
          }
        },

        {
          title: '价格',
          dataIndex: 'price',
          width: 100,
          align: 'center'
        },
        {
          title: '市盈率',
          dataIndex: 'pe',
          width: 80,
          align: 'center',
          customRender: (text, row, index) => {
            return Number(text) > 1 ? '1.0' : text
          }
        },
        {
          title: '是否显示',
          dataIndex: 'zt',
          width: 100,
          align: 'center',
          scopedSlots: { customRender: 'zt' }
        },
        {
          title: '股票类型',
          dataIndex: 'stockType',
          width: 100,
          align: 'center',
          scopedSlots: { customRender: 'stockType' }
        },
        {
          title: '类型',
          dataIndex: 'leixing',
          width: 100,
          align: 'center',
          scopedSlots: { customRender: 'leixing' }
        },
        {
          title: '申购时间',
          dataIndex: 'subscribeTime',
          width: 180,
          align: 'center',
          customRender: (text, row, index) => {
            return text ? moment(text).format('YYYY-MM-DD HH:mm:ss') : ''
          }
        },
        {
          title: '中签时间',
          dataIndex: 'subscriptionTime',
          width: 180,
          align: 'center',
          customRender: (text, row, index) => {
            return text ? moment(text).format('YYYY-MM-DD HH:mm:ss') : '- -'
          }
        },
        {
          title: '缴费时间',
          dataIndex: 'payTime',
          width: 180,
          align: 'center',
          customRender: (text, row, index) => {
            return text ? moment(text).format('YYYY-MM-DD HH:mm:ss') : '- -'
          }
        },
        {
          title: '上市时间',
          dataIndex: 'listDate',
          width: 180,
          align: 'center',
          customRender: (text, row, index) => {
            return text ? moment(text).format('YYYY-MM-DD HH:mm:ss') : '- -'
          }
        },
        {
          title: '操作',
          key: 'action',
          align: 'center',
          fixed: 'right',
          width: 100,
          scopedSlots: { customRender: 'action' }
        }
      ],
      //表头
      pagination: {
        total: 0,
        current: 1,
        pageSize: 10,//每页中显示10条数据
        showSizeChanger: true,
        pageSizeOptions: ['10', '20', '50', '100'],//每页中显示的数据
        onShowSizeChange: (current, pageSize) => this.onSizeChange(current, pageSize), // 改变每页数量时更新显示
        onChange: (page, pageSize) => this.onPageChange(page, pageSize),//点击页码事件
        showTotal: total => `共有 ${total} 条数据`  //分页中显示总的数据
      },
      loading: false,
      queryParam: {
        pageNum: 1,
        pageSize: 10,
        code: '',
        name: '',
        zt: undefined,
        type: undefined,
        pe: "",
      },
      datalist: [],
      labelCol: {
        xs: { span: 8 },
        sm: { span: 8 },
        md: { span: 8 }
      },
      wrapperCol: {
        xs: { span: 14 },
        sm: { span: 14 },
        md: { span: 14 }
      },
      addUserform: this.$form.createForm(this),
      addUserdialog: false,
      addUserDialogloading: false,

      fields: ['name', 'code', 'price', 'orderNumber', 'zt', 'subscribeTime', 'subscriptionTime', 'stockType', 'code', 'listDate', 'payTime', 'pe'],
      currentdetail: '',
      subscribeTime: '',
      subscriptionTime: '',
      listDate: '',
      payTime: ''
    }
  },
  created() {
    this.getlist()
  },
  methods: {
    getleixing(val) {
      if (moment(val).format('YYYY-MM-DD') == moment().format('YYYY-MM-DD')) {
        return '新股抢筹'
      } else {
        return '新股申购'
      }
    },
    getdeleteStock(val) {
      var that = this
      this.$confirm({
        title: '提示',
        content: '确认删除该新股吗？此操作不可恢复！',
        onOk() {
          var data = {
            id: val.newlistId
          }
          subscribeDel(data).then(res => {
            if (res.status == 0) {
              that.$message.success({ content: res.msg, duration: 2 })
              that.getinit()
            } else {
              that.$message.error({ content: res.msg })
            }
          })
        },
        onCancel() {
          console.log('Cancel')
        }
      })
    },
    getsubscribeTime(date, dateString) {
      this.subscribeTime = dateString
    },
    getsubscriptionTime(date, dateString) {
      this.subscriptionTime = dateString
    },
    getlistDate(date, dateString) {
      this.listDate = dateString
    },
    getpayTime(date, dateString) {
      this.payTime = dateString
    },
    showDetail(record) {
      this.detailData = { ...record }
      this.detailVisible = true
    },

    handleDetailCancel() {
      this.detailVisible = false
      this.detailData = {}
    },

    geteditStock(val) {
      this.currentdetail = val
      this.addUserdialog = true
      this.fields.forEach(v => this.addUserform.getFieldDecorator(v))
      this.addUserform.setFieldsValue(pick(val, this.fields))

      // 处理日期字段 - 使用 moment 对象设置表单值
      this.addUserform.setFieldsValue({
        subscriptionTime: val.subscriptionTime ? moment(val.subscriptionTime) : null,
        subscribeTime: val.subscribeTime ? moment(val.subscribeTime) : null,
        listDate: val.listDate ? moment(val.listDate) : null,
        payTime: val.payTime ? moment(val.payTime) : null
      })

      this.addUserform.setFieldsValue({ pe: val.pe ? Number(val.pe) : undefined })

      // 存储格式化的时间字符串用于提交
      this.subscribeTime = val.subscribeTime ? moment(val.subscribeTime).format('YYYY-MM-DD HH:mm:ss') : ''
      this.subscriptionTime = val.subscriptionTime ? moment(val.subscriptionTime).format('YYYY-MM-DD HH:mm:ss') : ''
      this.listDate = val.listDate ? moment(val.listDate).format('YYYY-MM-DD HH:mm:ss') : ''
      this.payTime = val.payTime ? moment(val.payTime).format('YYYY-MM-DD HH:mm:ss') : ''
    },
    CanceladdUserdialog() {
      this.addUserdialog = false
      const form = this.$refs.addUserform.form
      form.resetFields()
    },
    OkaddUserdialog() {
      const form = this.$refs.addUserform.form
      form.validateFields((errors, values) => {
        if (!errors) {
          if (this.currentdetail != '') {
            this.addUserDialogloading = true
            values.newlistId = this.currentdetail.newlistId
            values.subscriptionTime = moment(this.subscriptionTime).format('YYYY-MM-DD HH:mm:ss')
            values.subscribeTime = moment(this.subscribeTime).format('YYYY-MM-DD HH:mm:ss')
            values.listDate = moment(this.listDate).format('YYYY-MM-DD HH:mm:ss')
            values.payTime = moment(this.payTime).format('YYYY-MM-DD HH:mm:ss')
            if (!values.listDate) {
              values.listDate = moment().format('YYYY-MM-DD HH:mm:ss')
            }
            subscribeUpdate(values).then(res => {
              if (res.status == 0) {
                this.addUserdialog = false
                this.$message.success({ content: res.msg, duration: 2 })
                form.resetFields()
                this.getlist()
              } else {
                this.$message.error({ content: res.msg })
              }
              this.addUserDialogloading = false
            })
          } else {
            this.addUserDialogloading = true
            values.subscriptionTime = moment(this.subscriptionTime).format('YYYY-MM-DD HH:mm:ss')
            values.subscribeTime = moment(this.subscribeTime).format('YYYY-MM-DD HH:mm:ss')
            values.listDate = moment(this.listDate).format('YYYY-MM-DD HH:mm:ss')
            values.payTime = moment(this.payTime).format('YYYY-MM-DD HH:mm:ss')
            subscribeAdd(values).then(res => {
              if (res.status == 0) {
                this.addUserdialog = false
                this.$message.success({ content: res.msg, duration: 2 })
                form.resetFields()
                this.getinit()
              } else {
                this.$message.error({ content: res.msg })
              }
              this.addUserDialogloading = false
            })
          }
        }
      })
    },
    getqueryParam() {
      this.queryParam = {
        pageNum: 1,
        pageSize: 10,
        code: '',
        name: '',
        zt: undefined,
        type: undefined
      }
    },
    getinit() {
      this.getqueryParam()
      this.pagination.current = 1
      this.getlist()
    },
    getlist() {
      this.loading = true
      subscribeList(this.queryParam).then(res => {
        this.datalist = res.data.list
        this.pagination.total = res.data.total
        this.loading = false
      })
    },
    onPageChange(page, pageSize) {
      this.queryParam.pageNum = page
      this.pagination.current = page
      this.getlist()
    },
    onSizeChange(current, pageSize) {
      this.queryParam.pageNum = current
      this.pagination.current = page
      this.queryParam.pageSize = pageSize
      this.getlist()
    }
  }
}
</script>