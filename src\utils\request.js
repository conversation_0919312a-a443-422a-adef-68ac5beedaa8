import axios from 'axios'
import store from '@/store'
import router from '@/router'
import notification from 'ant-design-vue/es/notification'
import { VueAxios } from './axios'
import EventBus from './eventBus' // 导入事件总线
import CONFIG from '@/configs/index.js'
// 创建 axios 实例
const request = axios.create({
  // API 请求的默认前缀
  baseURL: CONFIG.VUE_APP_API_BASE_URL,
  //content-type: application/x-www-form-urlencoded
  headers: {
    'Content-Type': 'application/x-www-form-urlencoded'
  },
  timeout: 6000 // 请求超时时间
})

const errorHandler = (error) => {
  if (error.response) {
    const data = error.response.data
    // 从 localstorage 获取 token
    const token = sessionStorage.getItem('ACCESS_TOKEN')
    if (error.response.status === 403) {
      notification.error({
        message: 'Forbidden',
        description: data.msg,
      })
    }
    if (error.response.status === 401 && !(data.result && data.result.isLogin)) {
      notification.error({
        message: 'Unauthorized',
        description: 'Authorization verification failed',
      })
      if (token) {
        store.dispatch('Logout').then(() => {
          setTimeout(() => {
            window.location.reload()
          }, 1500)
        })
      }
    }
  }
  EventBus.$emit('loading-end') // 请求错误时也结束加载
  return Promise.reject(error)
}

// request interceptor
request.interceptors.request.use(config => {
  const token = sessionStorage.getItem('ACCESS_TOKEN')
  // 如果 token 存在
  // 让每个请求携带自定义 token 请根据实际情况自行修改
  if (token) {
    config.headers['satoken-agent'] = token
  }
  return config
}, errorHandler)

request.interceptors.response.use(
  async (response) => {
    try {
      const { status } = response.data
      console.log('response', response)
      console.log('status', status)
      if (status == -1 || status == -2 || status == -3 || status == -4 || status == -5) {
        notification.error({
          message: '重新登陆',
          description: '未登录或登录过期，请重新登录',
        })
        sessionStorage.removeItem('ACCESS_TOKEN')
        router.push('/login')
      } else if (!response.data) {
        notification.error({
          message: '网络错误',
          description: '网络错误，请稍后刷新页面重试！',
        })
      } else if (response.data.status !== 0) {
        notification.error({
          message: '发生错误',
          description: response.data.message || response.data.msg || '请求失败',
        })
      }
      return response.data
    } finally {
      EventBus.$emit('loading-end') // 无论成功失败都关闭 loading
    }
  },
  (error) => {
    // 响应失败也关闭 loading
    EventBus.$emit('loading-end')

    // 显示详细的错误信息
    if (error.response && error.response.data) {
      notification.error({
        message: `请求错误 (${error.response.status})`,
        description: error.response.data.message || error.response.data.msg || '请求失败'
      })
    }
    return errorHandler(error)
  }
)

const installer = {
  vm: {},
  install(Vue) {
    Vue.use(VueAxios, request)
  }
}

export default request

export {
  installer as VueAxios,
  request as axios
}
