# FROM nginx

# RUN rm /etc/nginx/conf.d/default.conf

# ADD deploy/nginx.conf /etc/nginx/conf.d/default.conf
# COPY dist/ /usr/share/nginx/html/

FROM nginxinc/nginx-unprivileged:1.27.3

# 将工作目录设置为Nginx的html目录
WORKDIR /usr/share/nginx/html

# 删除Nginx默认的配置文件
RUN rm /etc/nginx/conf.d/default.conf

# 将自定义的nginx配置文件复制到镜像中
COPY ./nginx.conf /etc/nginx/conf.d

# 将构建好的静态文件复制到镜像中
COPY dist/ .

# 暴露 80 端口
EXPOSE 8080
