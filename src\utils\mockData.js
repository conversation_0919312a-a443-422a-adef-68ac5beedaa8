// 模拟数据
export const mockUserInfo = {
  id: 1,
  name: '管理员',
  username: 'admin',
  password: '',
  avatar: 'https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png',
  status: 1,
  telephone: '',
  lastLoginIp: '127.0.0.1',
  lastLoginTime: 1534837621348,
  creatorId: 'admin',
  createTime: 1497160610259,
  deleted: 0,
  roleId: 'admin',
  lang: 'zh-CN',
  token: '4291d7da9005377ec9aec4a71ea837f',
  role: {
    id: 'admin',
    name: '管理员',
    describe: '拥有所有权限',
    status: 1,
    creatorId: 'system',
    createTime: 1497160610259,
    deleted: 0,
    permissions: [
      {
        roleId: 'admin',
        permissionId: 'dashboard',
        permissionName: '仪表板',
        actions: '[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"query","defaultCheck":false,"describe":"查询"},{"action":"get","defaultCheck":false,"describe":"详情"},{"action":"update","defaultCheck":false,"describe":"修改"},{"action":"delete","defaultCheck":false,"describe":"删除"}]',
        actionEntitySet: [
          {
            action: 'add',
            describe: '新增',
            defaultCheck: false
          },
          {
            action: 'query',
            describe: '查询',
            defaultCheck: false
          },
          {
            action: 'get',
            describe: '详情',
            defaultCheck: false
          },
          {
            action: 'update',
            describe: '修改',
            defaultCheck: false
          },
          {
            action: 'delete',
            describe: '删除',
            defaultCheck: false
          }
        ],
        actionList: ['add', 'query', 'get', 'update', 'delete'],
        dataAccess: null
      },
      {
        roleId: 'admin',
        permissionId: 'userlist',
        permissionName: '用户管理',
        actions: '[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"query","defaultCheck":false,"describe":"查询"}]',
        actionEntitySet: [
          {
            action: 'add',
            describe: '新增',
            defaultCheck: false
          },
          {
            action: 'query',
            describe: '查询',
            defaultCheck: false
          }
        ],
        actionList: ['add', 'query'],
        dataAccess: null
      },
      {
        roleId: 'admin',
        permissionId: 'agentlist',
        permissionName: '代理管理',
        actions: '[{"action":"add","defaultCheck":false,"describe":"新增"},{"action":"query","defaultCheck":false,"describe":"查询"}]',
        actionEntitySet: [
          {
            action: 'add',
            describe: '新增',
            defaultCheck: false
          },
          {
            action: 'query',
            describe: '查询',
            defaultCheck: false
          }
        ],
        actionList: ['add', 'query'],
        dataAccess: null
      },
      {
        roleId: 'admin',
        permissionId: 'profitdetails',
        permissionName: '利润明细',
        actions: '[{"action":"query","defaultCheck":false,"describe":"查询"}]',
        actionEntitySet: [
          {
            action: 'query',
            describe: '查询',
            defaultCheck: false
          }
        ],
        actionList: ['query'],
        dataAccess: null
      },
      {
        roleId: 'admin',
        permissionId: 'financing',
        permissionName: '持仓管理',
        actions: '[{"action":"query","defaultCheck":false,"describe":"查询"}]',
        actionEntitySet: [
          {
            action: 'query',
            describe: '查询',
            defaultCheck: false
          }
        ],
        actionList: ['query'],
        dataAccess: null
      },
      {
        roleId: 'admin',
        permissionId: 'fundrecords',
        permissionName: '资金管理',
        actions: '[{"action":"query","defaultCheck":false,"describe":"查询"}]',
        actionEntitySet: [
          {
            action: 'query',
            describe: '查询',
            defaultCheck: false
          }
        ],
        actionList: ['query'],
        dataAccess: null
      },
      {
        roleId: 'admin',
        permissionId: 'depositlist',
        permissionName: '入金记录',
        actions: '[{"action":"query","defaultCheck":false,"describe":"查询"}]',
        actionEntitySet: [
          {
            action: 'query',
            describe: '查询',
            defaultCheck: false
          }
        ],
        actionList: ['query'],
        dataAccess: null
      },
      {
        roleId: 'admin',
        permissionId: 'cashinglist',
        permissionName: '出金记录',
        actions: '[{"action":"query","defaultCheck":false,"describe":"查询"}]',
        actionEntitySet: [
          {
            action: 'query',
            describe: '查询',
            defaultCheck: false
          }
        ],
        actionList: ['query'],
        dataAccess: null
      }
    ]
  }
}

// 模拟路由数据
export const mockRoutes = [
  {
    path: '/dashboard',
    name: 'dashboard',
    component: 'BasicLayout',
    meta: { title: '仪表板', icon: 'dashboard', permission: ['dashboard'] },
    children: [
      {
        path: '/dashboard/workplace',
        name: 'workplace',
        component: 'Workplace',
        meta: { title: '工作台', permission: ['dashboard'] }
      }
    ]
  }
]
