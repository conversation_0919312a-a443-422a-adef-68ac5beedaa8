// eslint-disable-next-line
import { UserLayout, BasicLayout, BlankLayout } from '@/layouts'
import { bxAnaalyse } from '@/core/icons'

const RouteView = {
  name: 'RouteView',
  render: h => h('router-view')
}

export const asyncRouterMap = [
  {
    path: '/',
    name: 'index',
    component: BasicLayout,
    meta: { title: '首页' },
    redirect: '/dashboard/workplace',
    children: [
      {
        path: '/dashboard',
        name: 'dashboard',
        redirect: '/dashboard/workplace',
        component: RouteView,
        meta: { title: '仪表板', keepAlive: true, icon: bxAnaalyse, permission: ['dashboard'] },
        children: [
          {
            path: '/dashboard/workplace',
            name: 'Workplace',
            component: () => import('@/views/dashboard/Workplace'),
            meta: { title: '工作台', keepAlive: true, permission: ['dashboard'] }
          }
        ]
      },
      {
        path: '/userlist',
        redirect: '/userlist/index',
        component: RouteView,
        meta: { title: '用户管理', icon: 'user', permission: ['userlist'] },
        children: [
          {
            path: '/userlist/index',
            name: 'Userlist',
            component: () => import('@/views/userlist/index'),
            meta: { title: '用户列表', keepAlive: true, permission: ['userlist'] }
          },
        ]
      },
      {
        path: '/follow',
        redirect: '/follow/followList',
        component: RouteView,
        meta: { title: '跟单管理', icon: 'usergroup-delete', permission: ['follow'] },
        children: [
          {
            path: '/follow/followList',
            name: 'followList',
            component: () => import('@/views/follow/followList'),
            meta: { title: '跟单', keepAlive: true, permission: ['followList'] }
          },
          {
            path: '/follow/tradeDetail',
            name: 'FollowTradeDetail',
            component: () => import('@/views/follow/tradeDetail.vue'),
            meta: { title: '跟单交易详情', keepAlive: true, permission: ['followTradeDetail'] }
          }
        ]
      },
      // 持仓管理
      {
        path: '/position',
        redirect: '/position/financing',
        component: RouteView,
        meta: { title: '持仓管理', icon: 'money-collect', permission: ['financing'] },
        children: [
          {
            path: '/position/financing',
            name: 'financing',
            component: () => import('@/views/position/financing'),
            meta: { title: '持仓管理', keepAlive: true, permission: ['financing'] }
          },
        ]
      },
      // 新股管理
      {
        path: '/newshares',
        redirect: '/newshares/newshareslist',
        component: RouteView,
        meta: { title: '新股管理', icon: 'sliders', permission: ['newshareslist'] },
        children: [
          {
            path: '/newshares/newshareslist',
            name: 'newshareslist',
            component: () => import('@/views/newshares/newshareslist'),
            meta: { title: '新股列表', keepAlive: true, permission: ['newshareslist'] }
          },
          {
            path: '/newshares/newsharesrecord',
            name: 'newsharesrecord',
            component: () => import('@/views/newshares/newsharesrecord'),
            meta: { title: '新股申购记录', keepAlive: true, permission: ['newsharesrecord'] }
          },
        ]
      },
      // 资金管理
      {
        path: '/capital',
        redirect: '/capital/rechargeList',
        component: RouteView,
        meta: { title: '资金管理', icon: 'dollar', permission: ['rechargeList'] },
        children: [
          {
            path: '/capital/rechargeList',
            name: 'rechargeList',
            component: () => import('@/views/capital/rechargelist.vue'),
            meta: { title: '充值列表', keepAlive: true, permission: ['rechargeList'] }
          },
          {
            path: '/capital/withdrawallist',
            name: 'withdrawallist',
            component: () => import('@/views/capital/withdrawallist.vue'),
            meta: { title: '提现列表', keepAlive: true, permission: ['withdrawallist'] }
          },
          {
            path: '/capital/fundrecords',
            name: 'fundrecords',
            component: () => import('@/views/capital/fundrecords.vue'),
            meta: { title: '资金记录', keepAlive: true, permission: ['fundrecords'] }
          },
        ]
      },
    ]
  },
  {
    path: '*',
    redirect: '/404',
    hidden: true
  }
]

/**
 * 基础路由
 * @type { *[] }
 */
export const constantRouterMap = [
  {
    path: '/user',
    component: UserLayout,
    redirect: '/user/login',
    hidden: true,
    children: [
      {
        path: 'login',
        name: 'login',
        component: () => import(/* webpackChunkName: "user" */ '@/views/user/Login')
      },
      {
        path: 'register',
        name: 'register',
        component: () => import(/* webpackChunkName: "user" */ '@/views/user/Register')
      },
      {
        path: 'register-result',
        name: 'registerResult',
        component: () => import(/* webpackChunkName: "user" */ '@/views/user/RegisterResult')
      },

    ]
  },

  {
    path: '/404',
    component: () => import(/* webpackChunkName: "fail" */ '@/views/exception/404')
  }
]
