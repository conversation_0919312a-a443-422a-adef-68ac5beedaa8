// eslint-disable-next-line
import { UserLayout, BasicLayout, BlankLayout } from '@/layouts'
import { bxAnaalyse } from '@/core/icons'

const RouteView = {
  name: 'RouteView',
  render: h => h('router-view')
}

export const asyncRouterMap = [
  {
    path: '/',
    name: 'index',
    component: BasicLayout,
    meta: { title: 'menu.home' },
    redirect: '/dashboard/workplace',
    children: [
      {
        path: '/dashboard',
        name: 'dashboard',
        redirect: '/dashboard/workplace',
        component: RouteView,
        meta: { title: 'menu.dashboard', keepAlive: true, icon: bxAnaalyse, permission: ['dashboard'] },
        children: [
          {
            path: '/dashboard/workplace',
            name: 'Workplace',
            component: () => import('@/views/dashboard/Workplace'),
            meta: { title: 'menu.dashboard.workplace', keepAlive: true, permission: ['dashboard'] }
          }
        ]
      },
      {
        path: '/userlist',
        redirect: '/userlist/index',
        component: RouteView,
        meta: { title: '用户管理', icon: 'user', permission: ['userlist'] },
        children: [
          {
            path: '/userlist/index',
            name: 'Userlist',
            component: () => import('@/views/userlist/index'),
            meta: { title: '用户列表', keepAlive: true, permission: ['userlist'] }
          },
        ]
      },
      {
        path: '/agentlist',
        redirect: '/agentlist/index',
        component: RouteView,
        meta: { title: '代理管理', icon: 'team', permission: ['agentlist'] },
        children: [
          {
            path: '/agentlist/index',
            name: 'agentlist',
            component: () => import('@/views/agentlist/index'),
            meta: { title: '代理列表', keepAlive: true, permission: ['agentlist'] }
          },
        ]
      },
      {
        path: '/profitdetails',
        redirect: '/profitdetails/index',
        component: RouteView,
        meta: { title: '利润明细', icon: 'transaction', permission: ['profitdetails'] },
        children: [
          {
            path: '/profitdetails/index',
            name: 'profitdetails',
            component: () => import('@/views/profitdetails/index'),
            meta: { title: '明细列表', keepAlive: true, permission: ['profitdetails'] }
          },
        ]
      },
      // 持仓管理
      {
        path: '/position',
        redirect: '/position/financing',
        component: RouteView,
        meta: { title: '持仓管理', icon: 'money-collect', permission: ['financing'] },
        children: [
          {
            path: '/position/financing',
            name: 'financing',
            component: () => import('@/views/position/financing'),
            meta: { title: '持仓管理', keepAlive: true, permission: ['financing'] }
          },
        ]
      },
      // 资金管理
      {
        path: '/capital',
        redirect: '/capital/fundrecords',
        component: RouteView,
        meta: { title: '资金管理', icon: 'dollar', permission: ['fundrecords'] },
        children: [
          {
            path: '/capital/fundrecords',
            name: 'fundrecords',
            component: () => import('@/views/capital/fundrecords'),
            meta: { title: '资金明细', keepAlive: true, permission: ['fundrecords'] }
          },
        ]
      },
      {
        path: '/depositrecord',
        redirect: '/depositrecord/depositlist',
        component: RouteView,
        meta: { title: '入金记录', icon: 'import', permission: ['depositlist'] },
        children: [
          {
            path: '/depositrecord/depositlist',
            name: 'depositlist',
            component: () => import('@/views/depositrecord/depositlist'),
            meta: { title: '入金列表', keepAlive: true, permission: ['depositlist'] }
          },
        ]
      },
      {
        path: '/cashingrecord',
        redirect: '/cashingrecord/cashinglist',
        component: RouteView,
        meta: { title: '出金记录', icon: 'export', permission: ['cashinglist'] },
        children: [
          {
            path: '/cashingrecord/cashinglist',
            name: 'cashinglist',
            component: () => import('@/views/cashingrecord/cashinglist'),
            meta: { title: '出金列表', keepAlive: true, permission: ['cashinglist'] }
          },
        ]
      }
    ]
  },
  {
    path: '*',
    redirect: '/404',
    hidden: true
  }
]

/**
 * 基础路由
 * @type { *[] }
 */
export const constantRouterMap = [
  {
    path: '/user',
    component: UserLayout,
    redirect: '/user/login',
    hidden: true,
    children: [
      {
        path: 'login',
        name: 'login',
        component: () => import(/* webpackChunkName: "user" */ '@/views/user/Login')
      },
      {
        path: 'register',
        name: 'register',
        component: () => import(/* webpackChunkName: "user" */ '@/views/user/Register')
      },
      {
        path: 'register-result',
        name: 'registerResult',
        component: () => import(/* webpackChunkName: "user" */ '@/views/user/RegisterResult')
      },

    ]
  },

  {
    path: '/404',
    component: () => import(/* webpackChunkName: "fail" */ '@/views/exception/404')
  }
]
