import router, { resetRouter } from './router'
import store from './store'
import storage from 'store'
import NProgress from 'nprogress' // progress bar
import '@/components/NProgress/nprogress.less' // progress bar custom style
import notification from 'ant-design-vue/es/notification'
import { setDocumentTitle, domTitle } from '@/utils/domUtil'
import { ACCESS_TOKEN } from '@/store/mutation-types'


NProgress.configure({ showSpinner: false }) // NProgress Configuration

const allowList = ['login', 'register', 'registerResult'] // no redirect allowList
const loginRoutePath = '/user/login'
const defaultRoutePath = '/dashboard/workplace'
const UserlistRoutePath = '/user/userlist'

router.beforeEach((to, from, next) => {
  NProgress.start() // start progress bar
  to.meta && typeof to.meta.title !== 'undefined' && setDocumentTitle(`${to.meta.title} - ${domTitle}`)

  const token = sessionStorage.getItem('ACCESS_TOKEN')
  
  if (token) {
    if (to.path === loginRoutePath) {
      next({ path: UserlistRoutePath })
      NProgress.done()
    } else {
      // 如果路由还没有生成
      if (store.getters.addRouters.length === 0) {
        // 设置默认角色为admin
        store.commit('SET_ROLES', ['admin'])
        // 生成路由
        store.dispatch('GenerateRoutes', { role: ['admin'] }).then(() => {
          resetRouter()
          store.getters.addRouters.forEach(r => {
            router.addRoute(r)
          })
          next({ ...to, replace: true })
        })
      } else {
        next()
      }
    }
  } else {
    if (allowList.includes(to.name)) {
      // 在免登录名单，直接进入
      next()
    } else {
      next({ path: loginRoutePath, query: { redirect: to.fullPath } })
      NProgress.done() // if current page is login will not trigger afterEach hook, so manually handle it
    }
  }
})

router.afterEach(() => {
  NProgress.done() // finish progress bar
})
