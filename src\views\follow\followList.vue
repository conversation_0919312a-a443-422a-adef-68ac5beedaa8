<template>
  <div class="follow-list">
    <a-card :bordered="false">
      <!-- 待审核统计 -->
      <div class="pending-audit-info" style="margin-bottom: 16px;">
        <a-alert type="info" show-icon>
          <span slot="message">
            <!-- 待审核追加 <a style="font-weight: 600">{{ pendingAddAuditCount }}</a> 条 -->
            <a-button type="link" icon="reload" @click="fetchPendingAddAuditCount" :loading="pendingCountLoading"
              style="margin-left: 16px;">刷新</a-button>
          </span>
        </a-alert>
      </div>

      <!-- 搜索区域 -->
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="6" :sm="24">
              <a-form-item label="用户信息">
                <a-input v-model="queryParam.keyword" placeholder="用户姓名、手机号、账号" allowClear />
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <a-form-item label="跟单状态">
                <a-select v-model="queryParam.status" placeholder="请选择跟单状态" allowClear>
                  <a-select-option :value="1">待审核</a-select-option>
                  <a-select-option :value="2">已通过</a-select-option>
                  <a-select-option :value="3">已拒绝</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <a-form-item label="追加审核状态">
                <a-select v-model="queryParam.addStatus" placeholder="追加审核不限" allowClear>
                  <a-select-option :value="1">有待追加审核</a-select-option>
                  <a-select-option :value="0">无待追加审核</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <a-form-item label="时间范围">
                <a-range-picker v-model="queryParam.timeRange" style="width: 100%" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :md="24" :sm="24" style="text-align: right">
              <span class="table-page-search-submitButtons">
                <a-button type="primary" @click="$refs.table.refresh(true)">查询</a-button>
                <a-button style="margin-left: 8px" @click="resetSearchForm">重置</a-button>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>

      <!-- 表格区域 -->
      <s-table ref="table" size="default" rowKey="id" :columns="columns" :data="loadData" :alert="options.alert"
        :rowSelection="options.rowSelection" showPagination="auto" :scroll="{ x: 'max-content' }" :bordered="true">
        <!-- 跟单金额 -->
        <span slot="followAmount" slot-scope="text">
          {{ text && text.toFixed(2) }}
        </span>

        <!-- 追加金额 -->
        <span slot="addAmount" slot-scope="text">
          {{ text && text.toFixed(2) }}
        </span>

        <!-- 格式化金额 -->
        <span slot="amountFormat" slot-scope="text">
          {{ text && text.toFixed(2) }}
        </span>

        <!-- 跟单状态 -->
        <span slot="status" slot-scope="text">
          <span v-if="text === 1" style="color: #1890ff;">{{ text | statusFilter }}</span>
          <span v-else-if="text === 2" style="color: #52c41a;">{{ text | statusFilter }}</span>
          <span v-else-if="text === 3" style="color: #f5222d;">{{ text | statusFilter }}</span>
          <span v-else-if="text === 4" style="color: #faad14;">{{ text | statusFilter }}</span>
          <span v-else-if="text === 5" style="color: #722ed1;">{{ text | statusFilter }}</span>
          <span v-else-if="text === 6" style="color: #fa8c16;">{{ text | statusFilter }}</span>
          <span v-else>{{ text | statusFilter }}</span>
        </span>

        <!-- 追加审核状态 -->
        <span slot="addStatus" slot-scope="text, record">
          <span v-if="text === 1">{{ text | addStatusFilter }}</span>
          <span v-else-if="text === 2" style="color: #1890ff;">{{ text | addStatusFilter }}</span>
          <span v-else-if="text === 3" style="color: #f5222d;">{{ text | addStatusFilter }}</span>
          <span v-else>{{ text | addStatusFilter }}</span>
          <a v-if="record.isAdd === 1" style="margin-left: 8px; color: #faad14;"
            @click="handleAuditAdd(record)">追加审核</a>
        </span>

        <!-- 跟单类型 -->
        <span slot="followTypeDisplay" slot-scope="text, record">
          <a-tag v-if="record.packageId" color="blue">套餐产品</a-tag>
          <a-tag v-else color="orange">普通单单</a-tag>
        </span>

        <!-- 操作 -->
        <template slot="action" slot-scope="text, record">
          <a-space>
            <!-- <template v-if="record.status === 1">
              <a @click="handleAudit(record)">审核</a>
            </template>
<template v-if="record.status === 2">
              <a @click="handleModifyPosition(record)">修改仓位</a>
              <a-divider type="vertical" />
              <a @click="handleStopFollow(record)">中止跟单</a>
            </template>
<a-divider type="vertical" /> -->
            <a @click="handleViewDetails(record)">详情</a>
          </a-space>
        </template>
      </s-table>

      <!-- 详情弹窗 -->
      <a-modal title="跟单详情" :width="800" :visible="detailVisible" @cancel="handleDetailCancel" :footer="null">
        <a-descriptions bordered :column="2">
          <a-descriptions-item label="跟单ID">{{ detailData.id }}</a-descriptions-item>
          <a-descriptions-item label="单号">{{ detailData.followNo }}</a-descriptions-item>
          <a-descriptions-item label="会员账号">{{ detailData.userInfo?.userAccount }}</a-descriptions-item>
          <a-descriptions-item label="会员姓名">{{ detailData.userInfo?.userName }}</a-descriptions-item>
          <a-descriptions-item label="会员手机">{{ detailData.userInfo?.userPhone }}</a-descriptions-item>
          <a-descriptions-item label="跟单金额">{{ detailData.amount }}</a-descriptions-item>
          <a-descriptions-item label="追加金额">{{ detailData.addAmount || '-' }}</a-descriptions-item>
          <a-descriptions-item label="仓位比例">{{ detailData.positionRate ? (parseFloat(detailData.positionRate) *
            100).toFixed(1) + '%' : '-' }}</a-descriptions-item>

          <a-descriptions-item label="导师账号">{{ detailData.mentorInfo?.mentorAccount }}</a-descriptions-item>
          <a-descriptions-item label="导师姓名">{{ detailData.mentorInfo?.mentorName }}</a-descriptions-item>
          <a-descriptions-item label="导师手机">{{ detailData.mentorInfo?.mentorPhone }}</a-descriptions-item>
          <a-descriptions-item label="最低跟单金额">{{ detailData.minAmount || '-' }}</a-descriptions-item>
          <a-descriptions-item label="最高跟单金额">{{ detailData.maxAmount || '-' }}</a-descriptions-item>

          <a-descriptions-item label="申请时间">{{ detailData.applyTime }}</a-descriptions-item>
          <a-descriptions-item label="到期时间">{{ detailData.endTime }}</a-descriptions-item>
          <a-descriptions-item label="套餐天数">{{ detailData.packageDays ? `${detailData.packageDays} 个交易日` : '-'
            }}</a-descriptions-item>
          <a-descriptions-item label="跟单类型">
            <a-tag v-if="detailData.packageId" color="blue">套餐产品</a-tag>
            <a-tag v-else color="orange">普通单单</a-tag>
          </a-descriptions-item>

          <a-descriptions-item label="跟单状态">
            <span v-if="detailData.status === 1" style="color: #1890ff;">{{ detailData.status | statusFilter }}</span>
            <span v-else-if="detailData.status === 2" style="color: #52c41a;">{{ detailData.status | statusFilter
              }}</span>
            <span v-else-if="detailData.status === 3" style="color: #f5222d;">{{ detailData.status | statusFilter
              }}</span>
            <span v-else-if="detailData.status === 4" style="color: #faad14;">{{ detailData.status | statusFilter
              }}</span>
            <span v-else-if="detailData.status === 5" style="color: #722ed1;">{{ detailData.status | statusFilter
              }}</span>
            <span v-else-if="detailData.status === 6" style="color: #fa8c16;">{{ detailData.status | statusFilter
              }}</span>
            <span v-else>{{ detailData.status | statusFilter }}</span>
          </a-descriptions-item>
        </a-descriptions>
      </a-modal>

      <!-- 跟单审核弹窗 -->
      <a-modal title="跟单审核" :visible="auditVisible" :confirmLoading="auditLoading" @ok="handleAuditSubmit"
        @cancel="handleAuditCancel">
        <a-form :form="auditForm">
          <a-form-item label="审核结果" :labelCol="{ span: 6 }" :wrapperCol="{ span: 18 }">
            <a-radio-group v-decorator="['status', { rules: [{ required: true, message: '请选择审核结果' }] }]">
              <a-radio :value="2">通过</a-radio>
              <a-radio :value="3">拒绝</a-radio>
            </a-radio-group>
          </a-form-item>
          <a-form-item label="审核备注" :labelCol="{ span: 6 }" :wrapperCol="{ span: 18 }">
            <a-textarea v-decorator="['remark']" :rows="4" />
          </a-form-item>
        </a-form>
      </a-modal>

      <!-- 追加审核弹窗 -->
      <a-modal title="追加审核" :visible="auditAddVisible" :confirmLoading="auditAddLoading" @ok="handleAuditAddSubmit"
        @cancel="handleAuditAddCancel">
        <div style="margin-bottom: 16px;">
          <span>追加金额：<b>{{ appendDetail.amount }}</b></span>
          <span style="margin-left: 32px;">
            申请时间：
            <b>
              {{ appendDetail.createTime }}
            </b>
          </span>
        </div>
        <a-form :form="auditAddForm">
          <a-form-item label="审核结果" :labelCol="{ span: 6 }" :wrapperCol="{ span: 18 }">
            <a-radio-group v-decorator="['addStatus', { rules: [{ required: true, message: '请选择审核结果' }] }]">
              <a-radio :value="2">通过</a-radio>
              <a-radio :value="3">拒绝</a-radio>
            </a-radio-group>
          </a-form-item>
          <a-form-item label="审核备注" :labelCol="{ span: 6 }" :wrapperCol="{ span: 18 }">
            <a-textarea v-decorator="['addRemark']" :rows="4" />
          </a-form-item>
        </a-form>
      </a-modal>

      <!-- 修改仓位弹窗 -->
      <a-modal title="修改仓位" :visible="modifyPositionVisible" :confirmLoading="modifyPositionLoading"
        @ok="handleModifyPositionSubmit" @cancel="handleModifyPositionCancel">
        <a-form :form="modifyPositionForm">
          <a-form-item label="仓位比例" :labelCol="{ span: 6 }" :wrapperCol="{ span: 18 }">
            <a-input-number style="width: 100%" :min="0" :max="100" :step="1" addonAfter="%"
              placeholder="请输入仓位比例 (0-100)" v-decorator="[
                'positionRate',
                {
                  rules: [
                    { required: true, message: '请输入仓位比例' },
                    { type: 'number', message: '请输入有效的数字' }
                  ]
                }
              ]" />
          </a-form-item>
        </a-form>
      </a-modal>
    </a-card>
  </div>
</template>

<script>
import moment from 'moment'
import { STable } from '@/components'
import { pageList, audit, auditAdd, getPendingAddCount, updatePosition, queryAppendDetail } from '@/api/follow/follow'

export default {
  name: 'FollowList',
  components: {
    STable
  },
  data() {
    return {
      // 详情弹窗
      detailVisible: false,
      detailData: {},
      // 待审核追加数量
      pendingAddAuditCount: 0,
      pendingCountLoading: false,
      // 查询参数
      queryParam: {},
      // 表格列定义
      columns: [
        {
          title: 'ID',
          dataIndex: 'id',
          fixed: 'left'
        },
        {
          title: '单号',
          dataIndex: 'followNo'
        },
        {
          title: '跟单会员',
          children: [
            { title: '账号', dataIndex: 'userInfo.userAccount' },
            { title: '姓名', dataIndex: 'userInfo.userName' },
            { title: '手机号', dataIndex: 'userInfo.userPhone' },
            { title: '跟单金额', dataIndex: 'amount', scopedSlots: { customRender: 'amount' } },
            { title: '追加金额', dataIndex: 'addAmount', customRender: function (text) { return text != null ? text : '-'; } },
            {
              title: '仓位',
              dataIndex: 'positionRate',
              customRender: function (text) {
                return (text !== null && text !== undefined)
                  ? (parseFloat(text) * 100).toFixed(1) + '%'
                  : '-';
              }
            }
          ]
        },
        {
          title: '导师信息',
          children: [
            { title: '用户名', dataIndex: 'mentorInfo.mentorAccount' },
            { title: '姓名', dataIndex: 'mentorInfo.mentorName' },
            { title: '手机号', dataIndex: 'mentorInfo.mentorPhone' },
            { title: '跟单金额最低', dataIndex: 'minAmount', customRender: function (text) { return text != null ? text : '-'; } },
            { title: '跟单金额最高', dataIndex: 'maxAmount', customRender: function (text) { return text != null ? text : '-'; } }
          ]
        },
        { title: '申请时间', dataIndex: 'applyTime', customRender: function (text) { return text ? moment(text).format('YYYY-MM-DD HH:mm:ss') : '-'; } },
        { title: '合约到期时间', dataIndex: 'endTime', customRender: function (text) { return text ? moment(text).format('YYYY-MM-DD HH:mm:ss') : '-'; } },
        { title: '套餐信息', dataIndex: 'packageDays', customRender: function (text) { return text != null ? `${text} 个交易日` : '-'; } },
        {
          title: '跟单类型',
          key: 'followTypeDisplay',
          scopedSlots: { customRender: 'followTypeDisplay' },
          fixed: 'right',
          width: 100
        },
        {
          title: '追加待审核',
          dataIndex: 'addStatus',
          scopedSlots: { customRender: 'addStatus' },
          fixed: 'right',
          width: 120
        },
        {
          title: '跟单状态',
          dataIndex: 'status',
          scopedSlots: { customRender: 'status' },
          fixed: 'right',
          width: 100
        },
        {
          title: '操作',
          dataIndex: 'action',
          width: '150px',
          scopedSlots: { customRender: 'action' },
          fixed: 'right'
        }
      ],
      // 加载数据方法
      loadData: parameter => {
        const requestParameters = Object.assign({}, parameter, this.queryParam)
        // 处理时间范围
        if (requestParameters.timeRange && requestParameters.timeRange.length === 2) {
          requestParameters.beginTime = moment(requestParameters.timeRange[0]).format('YYYY-MM-DD HH:mm:ss')
          requestParameters.endTime = moment(requestParameters.timeRange[1]).format('YYYY-MM-DD HH:mm:ss')
          delete requestParameters.timeRange
        }
        return pageList(requestParameters)
          .then(res => {
            if (res && res.status === 0 && res.data && res.data.list) {
              return {
                data: res.data.list,
                pageSize: res.data.pageSize || 10,
                pageNo: res.data.pageNum || 1,
                totalCount: res.data.total || 0,
                totalPage: res.data.pages || 1
              }
            } else {
              return {
                data: [],
                pageSize: parameter.pageSize || 10,
                pageNo: 1,
                totalCount: 0,
                totalPage: 1
              }
            }
          })
          .catch(() => {
            return {
              data: [],
              pageSize: parameter.pageSize || 10,
              pageNo: 1,
              totalCount: 0,
              totalPage: 1
            }
          })
      },
      // 表格配置
      options: {
        alert: false,
        rowSelection: null
      },
      // 跟单审核弹窗
      auditVisible: false,
      auditLoading: false,
      auditForm: this.$form.createForm(this),
      // 追加审核弹窗
      auditAddVisible: false,
      auditAddLoading: false,
      auditAddForm: this.$form.createForm(this, { name: 'audit_add_form' }),
      // 修改仓位弹窗
      modifyPositionVisible: false,
      modifyPositionLoading: false,
      modifyPositionForm: this.$form.createForm(this, { name: 'modify_position_form' }),
      currentRecord: null,
      appendDetail: {
        amount: null,
        applyTime: null
      }
    }
  },
  filters: {
    //1-待审核，2-跟单中，3-被驳回，4-已撤销，5-已结束，6-已中止
    statusFilter(status) {
      const statusMap = {
        1: '待审核',
        2: '跟单中',
        3: '被驳回',
        4: '已撤销',
        5: '已结束',
        6: '已中止',
      }
      return statusMap[status] || '未知'
    },
    statusTypeFilter(status) {
      const statusMap = {
        1: 'processing',
        2: 'success',
        3: 'error'
      }
      return statusMap[status] || 'default'
    },
    addStatusFilter(status) {
      const statusMap = {
        1: '待审核',
        2: '已通过',
        3: '已拒绝'
      }
      return statusMap[status] || '-'
    },
    addStatusTypeFilter(status) {
      const statusMap = {
        1: 'warning',
        2: 'success',
        3: 'error'
      }
      return statusMap[status] || 'default'
    }
  },
  created() {
    // this.fetchPendingAddAuditCount()
  },
  methods: {
    fetchPendingAddAuditCount() {
      this.pendingCountLoading = true
      getPendingAddCount().then(res => {
        this.pendingAddAuditCount = res.data || 0
      }).finally(() => {
        this.pendingCountLoading = false
      })
    },
    resetSearchForm() {
      this.queryParam = {}
      this.$refs.table.refresh(true)
    },
    handleAudit(record) {
      this.currentRecord = { ...record }
      this.auditVisible = true
      // 清空表单
      this.$nextTick(() => {
        this.auditForm.resetFields()
      })
    },
    handleAuditSubmit() {
      this.auditForm.validateFields((err, values) => {
        if (!err) {
          this.auditLoading = true
          const params = {
            id: this.currentRecord.id,
            status: values.status,
            remark: values.remark || ''
          }
          audit(params).then(() => {
            this.$message.success('审核成功')
            this.auditVisible = false
            this.$refs.table.refresh()
            this.fetchPendingAddAuditCount() // 审核后可能影响待审核数
          }).finally(() => {
            this.auditLoading = false
          })
        }
      })
    },
    handleAuditCancel() {
      this.auditVisible = false
    },
    handleAuditAdd(record) {
      this.currentRecord = { ...record }
      // 先清空
      this.appendDetail = { amount: null, createTime: null }
      // 调接口
      queryAppendDetail({ id: record.id }).then(res => {
        if (res && res.status === 0 && res.data) {
          console.log('queryAppendDetail res:', res.data)
          this.appendDetail.amount = res.data.amount
          this.appendDetail.createTime = res.data.createTime
        }
        this.auditAddVisible = true
        this.$nextTick(() => {
          this.auditAddForm.resetFields()
        })
      })
    },
    handleAuditAddSubmit() {
      this.auditAddForm.validateFields((err, values) => {
        if (!err) {
          this.auditAddLoading = true
          const params = {
            id: this.currentRecord.id,
            status: values.addStatus,
            addRemark: values.addRemark || ''
          }
          auditAdd(params).then(() => {
            this.$message.success('追加审核成功')
            this.auditAddVisible = false
            this.$refs.table.refresh()
            this.fetchPendingAddAuditCount() // 刷新待审核数
          }).finally(() => {
            this.auditAddLoading = false
          })
        }
      })
    },
    handleAuditAddCancel() {
      this.auditAddVisible = false
    },
    handleModifyPosition(record) {
      this.currentRecord = { ...record }; // Store the current record
      this.modifyPositionVisible = true;
      // Set initial value in the form (百分比)
      this.$nextTick(() => {
        this.modifyPositionForm.setFieldsValue({
          positionRate: record.positionRate != null ? (parseFloat(record.positionRate) * 100) : 0
        });
      });
    },
    handleModifyPositionCancel() {
      this.modifyPositionVisible = false;
      this.modifyPositionForm.resetFields();
    },
    handleModifyPositionSubmit() {
      this.modifyPositionForm.validateFields((err, values) => {
        if (!err) {
          this.modifyPositionLoading = true;
          const params = {
            id: this.currentRecord.id,
            // 提交时除以100
            positionRate: values.positionRate / 100
          };
          updatePosition(params).then(res => {
            if (res.status === 0) { // Check backend success status
              this.$message.success('修改仓位成功');
              this.modifyPositionVisible = false;
              this.$refs.table.refresh(); // Refresh table data
            } else {
              this.$message.error(res.msg || '修改仓位失败');
            }
          }).catch(error => {
            console.error("Error updating position:", error);
            this.$message.error('修改仓位时发生错误');
          }).finally(() => {
            this.modifyPositionLoading = false;
          });
        }
      });
    },
    handleStopFollow(record) {
      // Placeholder for stop follow functionality
      console.log('Stop Follow clicked for record:', record);
      // Usually requires a confirmation modal
      this.$confirm({
        title: '确认中止跟单?',
        content: `您确定要中止单号 ${record.followNo} 的跟单吗? 此操作不可逆。`,
        okText: '确认中止',
        okType: 'danger',
        cancelText: '取消',
        onOk: () => {
          console.log('Stopping follow for:', record.id);
          // TODO: Call API to stop the follow
          // Example: stopFollowApi({ id: record.id }).then(() => { ... })
          this.$message.success('中止操作待实现');
          // Potentially refresh table: this.$refs.table.refresh()
        },
        onCancel() { }
      });
    },
    handleViewProduct(record) {
      // 假设产品ID字段为 productId 或 relateProductId
      const productId = record.productId || record.relateProductId
      if (productId) {
        // 根据你的路由配置调整路径
        this.$router.push({ path: `/product/detail/${productId}` })
      } else {
        this.$message.warning('未找到关联的产品ID')
      }
    },

    handleViewDetails(record) {
      this.detailData = { ...record }
      this.detailVisible = true
    },

    handleDetailCancel() {
      this.detailVisible = false
      this.detailData = {}
    },
    handleDetail(record) {
      // 根据你的路由配置调整路径
      this.$router.push({ path: `/follow/detail/${record.id}` })
    }
  }
}
</script>

<style lang="less" scoped>
.follow-list {
  .pending-audit-info {
    .ant-alert-info {
      background-color: #e6f7ff;
      border-color: #91d5ff;
    }
  }

  .table-operator {
    margin-bottom: 18px;
  }

  .table-page-search-wrapper {
    .ant-form-inline {
      .ant-form-item {
        display: flex;
        margin-bottom: 24px;
        margin-right: 0;

        .ant-form-item-control-wrapper {
          flex: 1 1;
          display: inline-block;
          vertical-align: middle;
        }

        >.ant-form-item-label {
          line-height: 32px;
          padding-right: 8px;
          width: auto;
        }
      }
    }

    .table-page-search-submitButtons {
      display: block;
      margin-bottom: 24px;
      white-space: nowrap;
    }
  }
}
</style>