<template>
    <page-header-wrapper>
        <a-card :bordered="false">
            <a-card :bordered="false">
                <div class="table-page-search-wrapper">
                    <a-form layout="inline">
                        <a-row :gutter="48">
                            <a-col :md="8" :sm="24">
                                <a-form-item label="用户ID">
                                    <a-input-number v-model="queryParam.userId" style="width: 100%"
                                        placeholder="请输入用户ID" />
                                </a-form-item>
                            </a-col>
                            <a-col :md="8" :sm="24">
                                <a-form-item label="账户类型">
                                    <a-select v-model="queryParam.amountType" placeholder="请选择账户类型" allowClear
                                        @change="handleAmountTypeChange">
                                        <a-select-option v-for="item in amountTypeOptions" :key="item.value"
                                            :value="item.value">{{ item.label }}</a-select-option>
                                    </a-select>
                                </a-form-item>
                            </a-col>
                            <a-col :md="8" :sm="24">
                                <a-form-item label="订单类型">
                                    <a-select v-model="queryParam.type" placeholder="请选择订单类型" allowClear>
                                        <a-select-option v-for="item in orderTypeOptions" :key="item.value"
                                            :value="item.value">{{ item.label }}</a-select-option>
                                    </a-select>
                                </a-form-item>
                            </a-col>
                        </a-row>
                        <a-row :gutter="48">
                            <a-col :md="8" :sm="24">
                                <a-form-item label="收支类型">
                                    <a-select v-model="queryParam.accountType" placeholder="请选择收支类型" allowClear>
                                        <a-select-option :value="1">收入</a-select-option>
                                        <a-select-option :value="2">支出</a-select-option>
                                    </a-select>
                                </a-form-item>
                            </a-col>
                            <a-col :md="8" :sm="24">
                                <a-form-item label="用户昵称">
                                    <a-input v-model="queryParam.nickName" placeholder="请输入用户昵称" />
                                </a-form-item>
                            </a-col>
                            <a-col :md="16" :sm="24">
                                <a-form-item>
                                    <span class="table-page-search-submitButtons">
                                        <a-button @click="resetQuery" icon="redo">重置</a-button>
                                        <a-button type="primary" icon="search" style="margin-left: 8px"
                                            @click="getList">查询</a-button>
                                    </span>
                                </a-form-item>
                            </a-col>
                        </a-row>
                    </a-form>
                </div>
            </a-card>
            <a-table bordered :loading="loading" :pagination="pagination" :columns="columns" :data-source="dataList"
                :scroll="{ x: 1200 }" size="middle" :rowKey="record => record.id" class="custom-table">
                <!-- 金额类型 -->
                <span slot="amountType" slot-scope="text">
                    <a-tag v-if="text === 1" color="blue">平台账户</a-tag>
                    <a-tag v-else-if="text === 2" color="purple">证券账户</a-tag>
                    <a-tag v-else color="default">未知</a-tag>
                </span>

                <!-- 订单类型 -->
                <span slot="type" slot-scope="text, record">
                    <a-tag>{{ record.typeDesc }}</a-tag>
                </span>

                <!-- 收支类型 -->
                <span slot="accountType" slot-scope="text, record">
                    <a-tag :color="text === 1 ? 'green' : 'red'">{{ record.accountTypeDesc }}</a-tag>
                </span>

                <!-- 帐变类型 -->
                <span slot="type" slot-scope="text, record">
                    <a-tag>{{ record.typeDesc }}</a-tag>
                </span>

                <!-- 变更金额 -->
                <span slot="amount" slot-scope="text, record">
                    <span :class="record.accountType === 1 ? 'income-amount' : 'expense-amount'">
                        {{ record.accountType === 1 ? '+' : '-' }}{{ text }}
                    </span>
                </span>

                <!-- 变更前后金额 -->
                <span slot="amountChange" slot-scope="text, record">
                    {{ record.oldAmount }} → {{ record.newAmount }}
                </span>

                <!-- 创建时间 -->
                <span slot="createTime" slot-scope="text">
                    {{ text ? moment(text).format('YYYY-MM-DD HH:mm:ss') : '' }}
                </span>
                <!-- 操作 -->
                <template slot="action" slot-scope="text, record">
                    <a href="javascript:;" @click="showDetail(record)">详情</a>
                </template>
            </a-table>

            <!-- 详情弹窗 -->
            <a-modal
                title="资金记录详情"
                :width="800"
                :visible="detailVisible"
                v-if="detailVisible"
                :footer="null"
                @cancel="handleDetailCancel"
            >
                <a-descriptions bordered :column="2">
                    <a-descriptions-item label="用户信息">
                        <div>
                            <span style="margin-right:10px">{{ detailData.userName }}（ID: {{ detailData.userId }}）</span>
                            <br />
                            昵称：{{ detailData.nickName }}
                        </div>
                    </a-descriptions-item>
                    <a-descriptions-item label="订单号">{{ detailData.orderNo }}</a-descriptions-item>
                    
                    <a-descriptions-item label="账户类型">
                        <a-tag v-if="detailData.amountType === 1" color="blue">平台账户</a-tag>
                        <a-tag v-else-if="detailData.amountType === 2" color="purple">证券账户</a-tag>
                        <a-tag v-else color="default">未知</a-tag>
                    </a-descriptions-item>
                    <a-descriptions-item label="订单类型">
                        <a-tag>{{ detailData.typeDesc }}</a-tag>
                    </a-descriptions-item>

                    <a-descriptions-item label="收支类型">
                        <a-tag :color="detailData.accountType === 1 ? 'green' : 'red'">
                            {{ detailData.accountTypeDesc }}
                        </a-tag>
                    </a-descriptions-item>
                    <a-descriptions-item label="变更金额">
                        <span :class="detailData.accountType === 1 ? 'income-amount' : 'expense-amount'">
                            {{ detailData.accountType === 1 ? '+' : '-' }}{{ detailData.amount }}
                        </span>
                    </a-descriptions-item>

                    <a-descriptions-item label="变更前金额">{{ detailData.oldAmount }}</a-descriptions-item>
                    <a-descriptions-item label="变更后金额">{{ detailData.newAmount }}</a-descriptions-item>

                    <a-descriptions-item label="操作人">{{ detailData.operator }}</a-descriptions-item>
                    <a-descriptions-item label="变更时间">{{ detailData.createTime ? moment(detailData.createTime).format('YYYY-MM-DD HH:mm:ss') : '' }}</a-descriptions-item>

                    <a-descriptions-item label="备注" :span="2">{{ detailData.remark || '-' }}</a-descriptions-item>
                </a-descriptions>
            </a-modal>
        </a-card>
    </page-header-wrapper>
</template>

<script>
import { cashlist, getAmountChangeType } from '@/api/capital'
import moment from 'moment'

export default {
    name: 'FundRecords',
    data() {
        return {
            // 表格列定义
            columns: [
                {
                    title: 'ID',
                    dataIndex: 'id',
                    align: 'center',
                    width: 80,
                    fixed: 'left'
                },
                {
                    title: '用户信息',
                    dataIndex: 'userName',
                    align: 'center',
                    width: 180,
                    customRender: (text, row) => {
                        return `${text || ''}（ID: ${row.userId || ''}）`
                    }
                },
                {
                    title: '订单号',
                    dataIndex: 'orderNo',
                    align: 'center',
                    width: 200
                },
                {
                    title: '账户类型',
                    dataIndex: 'amountType',
                    align: 'center',
                    width: 120,
                    scopedSlots: { customRender: 'amountType' }
                },
                {
                    title: '订单类型',
                    dataIndex: 'orderTypeDesc',
                    align: 'center',
                    width: 120,
                    scopedSlots: { customRender: 'orderTypeDesc' }
                },
                {
                    title: '收支类型',
                    dataIndex: 'accountType',
                    align: 'center',
                    width: 120,
                    scopedSlots: { customRender: 'accountType' }
                },
                {
                    title: '帐变类型',
                    dataIndex: 'type',
                    align: 'center',
                    width: 120,
                    scopedSlots: { customRender: 'type' }
                },
                {
                    title: '变更金额',
                    dataIndex: 'amount',
                    align: 'center',
                    width: 120,
                    scopedSlots: { customRender: 'amount' }
                },
                {
                    title: '变更前后金额',
                    dataIndex: 'oldAmount',
                    align: 'center',
                    width: 180,
                    scopedSlots: { customRender: 'amountChange' }
                },
                {
                    title: '备注',
                    dataIndex: 'remark',
                    align: 'center',
                    width: 200,
                    ellipsis: true
                },
                {
                    title: '操作人',
                    dataIndex: 'operator',
                    align: 'center',
                    width: 120
                },
                {
                    title: '变更时间',
                    dataIndex: 'createTime',
                    align: 'center',
                    width: 180,
                    scopedSlots: { customRender: 'createTime' }
                },
                {
                    title: '操作',
                    dataIndex: 'action',
                    align: 'center',
                    width: 100,
                    fixed: 'right',
                    scopedSlots: { customRender: 'action' }
                }
            ],
            // 分页配置
            pagination: {
                total: 0,
                current: 1,
                pageSize: 10,
                showSizeChanger: true,
                pageSizeOptions: ['10', '20', '50', '100'],
                showTotal: total => `共 ${total} 条记录`,
                onChange: (page, pageSize) => this.handlePageChange(page, pageSize),
                onShowSizeChange: (current, size) => this.handleSizeChange(current, size)
            },
            // 加载状态
            loading: false,
            // 查询参数
            queryParam: {
                page: 1,
                size: 10,
                userId: undefined,
                nickName: undefined,
                amountType: undefined,
                type: undefined,
                accountType: undefined
            },
            // 数据列表
            dataList: [],
            moment,
            amountTypeOptions: [
                { value: 1, label: '平台账户' },
                { value: 2, label: '证券账户' }
            ],
            orderTypeOptions: [],
            allOrderTypeOptions: {},
            // 详情弹窗相关
            detailVisible: false,
            detailData: {}
        }
    },
    created() {
        this.getAmountChangeTypeOptions()
        this.getList()
    },
    methods: {
        // 获取订单类型选项
        getAmountChangeTypeOptions() {
            getAmountChangeType().then(res => {
                if (res && res.success && res.data) {
                    // 构建 allOrderTypeOptions: {1: [...], 2: [...]}
                    this.allOrderTypeOptions = {}
                    res.data.forEach(item => {
                        this.allOrderTypeOptions[item.amountType] = (item.types || []).map(t => ({
                            value: t.type,
                            label: t.typeName
                        }))
                    })
                    // 优先用当前选中的账户类型
                    const curType = this.queryParam.amountType || 1
                    this.orderTypeOptions = this.allOrderTypeOptions[curType] || []
                }
            })
        },
        // 账户类型变化时联动订单类型
        handleAmountTypeChange(val) {
            this.queryParam.type = undefined
            this.orderTypeOptions = this.allOrderTypeOptions[val] || []
        },
        // 获取列表数据
        getList() {
            this.loading = true

            // 构建查询参数
            const params = {
                page: this.pagination.current,
                size: this.pagination.pageSize,
                ...this.queryParam
            }

            // 移除undefined的参数
            Object.keys(params).forEach(key => {
                if (params[key] === undefined) {
                    delete params[key]
                }
            })

            cashlist(params).then(res => {
                if (res.success) {
                    const pageInfo = res.data
                    this.dataList = pageInfo.list || []
                    this.pagination.total = pageInfo.total || 0
                } else {
                    this.$message.error(res.msg || '获取数据失败')
                    this.dataList = []
                    this.pagination.total = 0
                }
            }).catch(err => {
                console.error('获取资金记录失败', err)
                this.$message.error('获取资金记录失败')
                this.dataList = []
                this.pagination.total = 0
            }).finally(() => {
                this.loading = false
            })
        },

        // 重置查询条件
        resetQuery() {
            this.queryParam = {
                page: 1,
                size: 10,
                userId: undefined,
                nickName: undefined,
                amountType: undefined,
                type: undefined,
                accountType: undefined
            }
            this.pagination.current = 1
            this.getList()
        },

        // 页码变化
        handlePageChange(page, pageSize) {
            this.pagination.current = page
            this.pagination.pageSize = pageSize
            this.getList()
        },

        // 每页条数变化
        handleSizeChange(current, size) {
            this.pagination.current = 1
            this.pagination.pageSize = size
            this.getList()
        },

        // 显示详情
        showDetail(record) {
            this.detailData = { ...record }
            this.detailVisible = true
        },

        // 关闭详情弹窗
        handleDetailCancel() {
            this.detailVisible = false
            this.detailData = {}
        }
    }
}
</script>

<style lang="less" scoped>
.income-amount {
    color: #52c41a;
    font-weight: bold;
}

.expense-amount {
    color: #f5222d;
    font-weight: bold;
}

.table-page-search-wrapper {
    .ant-form-inline {
        .ant-form-item {
            display: flex;
            margin-bottom: 12px;
            margin-right: 0;

            .ant-form-item-control-wrapper {
                flex: 1 1;
                display: inline-block;
                vertical-align: middle;
            }

            >.ant-form-item-label {
                width: auto;
                line-height: 32px;
                padding-right: 8px;
            }
        }
    }
}

.table-page-search-submitButtons {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 24px;

    @media screen and (max-width: 576px) {
        display: block;
        margin-top: 12px;

        .ant-btn {
            width: 100%;
            margin-left: 0 !important;
            margin-bottom: 8px;
        }
    }
}

.custom-table {
    margin-top: 16px;

    @media screen and (max-width: 1199px) {
        :deep(.ant-table) {
            width: 100%;
            overflow-x: auto;
        }

        :deep(.ant-table-body) {
            overflow-x: auto !important;
        }
    }
}
</style>
