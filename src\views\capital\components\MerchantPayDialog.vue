<template>
  <div>
    <a-modal :title="isEdit ? '编辑支付商户' : '添加支付商户'" :width="900" :visible="visible" :confirmLoading="merchantFormLoading"
      @ok="handleOk" @cancel="handleCancel">
      <a-form :form="merchantForm" ref="merchantForm" layout="horizontal" class="merchant-form">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="商户名称" :labelCol="{ span: 6 }" :wrapperCol="{ span: 18 }">
              <a-input v-decorator="['name', { rules: [{ required: true, message: '请输入商户名称' }] }]"
                placeholder="请输入商户名称" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="支付代码" :labelCol="{ span: 6 }" :wrapperCol="{ span: 18 }">
              <a-input v-decorator="['code', { rules: [{ required: true, message: '请输入支付代码' }] }]"
                placeholder="请输入支付代码" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="币种" :labelCol="{ span: 6 }" :wrapperCol="{ span: 18 }">
              <a-input v-decorator="['currency', { rules: [{ required: true, message: '请输入币种' }] }]"
                placeholder="请输入币种，如：USD、CNY" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="汇率" :labelCol="{ span: 6 }" :wrapperCol="{ span: 18 }">
              <a-input-number v-decorator="['rate', { rules: [{ required: true, message: '请输入汇率' }] }]"
                placeholder="请输入汇率" :min="0" :step="0.01" :precision="4" style="width: 100%" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="充值区间" :labelCol="{ span: 6 }" :wrapperCol="{ span: 18 }">
              <a-input v-decorator="['rechargeRange', { rules: [{ required: true, message: '请输入充值区间' }] }]"
                placeholder="如：100-10000" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="提现区间" :labelCol="{ span: 6 }" :wrapperCol="{ span: 18 }">
              <a-input v-decorator="['withdrawRange', { rules: [{ required: true, message: '请输入提现区间' }] }]"
                placeholder="如：100-5000" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="商户状态" :labelCol="{ span: 6 }" :wrapperCol="{ span: 18 }">
              <a-radio-group v-decorator="['status', { rules: [{ required: true, message: '请选择商户状态' }] }]">
                <a-radio :value="1">正常</a-radio>
                <a-radio :value="0">冻结</a-radio>
              </a-radio-group>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="排序" :labelCol="{ span: 6 }" :wrapperCol="{ span: 18 }">
              <a-input-number v-decorator="['sort', { rules: [{ required: true, message: '请输入排序值' }] }]"
                placeholder="请输入排序值" :min="0" style="width: 100%" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="充值显示" :labelCol="{ span: 6 }" :wrapperCol="{ span: 18 }">
              <a-radio-group v-decorator="['isRechange', { rules: [{ required: true, message: '请选择充值显示状态' }] }]">
                <a-radio :value="1">显示</a-radio>
                <a-radio :value="0">不显示</a-radio>
              </a-radio-group>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="提现显示" :labelCol="{ span: 6 }" :wrapperCol="{ span: 18 }">
              <a-radio-group v-decorator="['isWithdraw', { rules: [{ required: true, message: '请选择提现显示状态' }] }]">
                <a-radio :value="1">显示</a-radio>
                <a-radio :value="0">不显示</a-radio>
              </a-radio-group>
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item label="回调地址" :labelCol="{ span: 3 }" :wrapperCol="{ span: 21 }">
          <a-input v-decorator="['callBackIp', { rules: [{ required: true, message: '请输入回调地址' }] }]"
            placeholder="请输入回调地址" />
        </a-form-item>

        <a-form-item label="支付参数" :labelCol="{ span: 3 }" :wrapperCol="{ span: 21 }">
          <a-textarea v-decorator="['paramStr']" :auto-size="{ minRows: 3, maxRows: 6 }"
            placeholder="请输入支付参数（JSON格式）" />
        </a-form-item>

        <a-form-item label="商户头像" :labelCol="{ span: 3 }" :wrapperCol="{ span: 21 }">
          <a-upload name="upload_file" list-type="picture-card" accept=".jpg,.jpeg,.png" class="avatar-uploader"
            :showUploadList="false" :customRequest="customRequest"
            v-decorator="['avatar', { rules: [{ required: true, message: '请上传商户头像' }] }]">
            <img v-if="merchantAvatar" :src="merchantAvatar" alt="商户头像" style="width: 100%" />
            <div v-else>
              <a-icon :type="imgLoading ? 'loading' : 'plus'" />
              <div class="ant-upload-text">上传</div>
            </div>
          </a-upload>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script>
import { adminUpload } from '@/api/allsetting'

export default {
  name: 'MerchantPayDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    loading: {
      type: Boolean,
      default: false
    },
    model: {
      type: Object,
      default: () => ({})
    },
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      merchantForm: this.$form.createForm(this),
      merchantFormLoading: false,
      merchantAvatar: '',
      imgLoading: false
    }
  },

  watch: {
    visible(val) {
      if (val) {
        this.$nextTick(() => {
          this.initFormData();
        });
      }
    },
    model: {
      handler(newVal) {
        if (this.visible && newVal) {
          this.$nextTick(() => {
            this.initFormData();
          });
        }
      },
      deep: true
    }
  },

  mounted() {
    if (this.visible) {
      this.initFormData();
    }
  },

  methods: {
    // 取消操作
    handleCancel() {
      this.merchantAvatar = ''
      const form = this.$refs.merchantForm.form
      form.resetFields()
      this.$emit('cancel')
    },

    // 确认操作
    handleOk() {
      const form = this.$refs.merchantForm.form
      form.validateFields((errors, values) => {
        if (!errors) {
          this.merchantFormLoading = true

          // 如果是编辑模式，需要添加ID
          if (this.isEdit && this.model.id) {
            values.id = this.model.id
          }

          // 将数据发送给父组件
          this.$emit('submit', values)

          // 重置表单
          this.merchantAvatar = ''
          form.resetFields()

          // 关闭对话框
          this.$emit('cancel')

          // 重置加载状态
          this.merchantFormLoading = false
        }
      })
    },

    // 初始化表单数据
    initFormData() {
      this.$nextTick(() => {
        if (this.isEdit && this.model) {
          // 编辑模式
          this.merchantAvatar = this.model.avatar || '';
          this.merchantForm.setFieldsValue({
            ...this.model,
            status: this.model.status || 1,
            isRechange: this.model.isRechange || 1,
            isWithdraw: this.model.isWithdraw || 1,
            sort: this.model.sort || 0,
            rate: this.model.rate || 1.0000
          });
        } else {
          // 添加模式默认值
          this.merchantForm.setFieldsValue({
            status: 1,
            isRechange: 1,
            isWithdraw: 1,
            sort: 0,
            rate: 1.0000
          });
        }
      });
    },

    // 自定义上传方法
    customRequest(data) {
      this.imgLoading = true
      const formData = new FormData()
      formData.append('upload_file', data.file)

      adminUpload(formData).then(res => {
        if (res.status === 0) {
          this.merchantAvatar = res.data.url
          this.merchantForm.setFieldsValue({
            avatar: res.data.url
          })
          this.$message.success('上传成功')
        } else {
          this.$message.error(res.msg || '上传失败，请检查图片类型!')
        }
        this.imgLoading = false
      }).catch(err => {
        console.error('Upload image error:', err)
        this.$message.error('上传失败，请稍后再试!')
        this.imgLoading = false
      })
    }
  }
}
</script>

<style lang="less" scoped>
.merchant-form .ant-form-item-label {
  text-align: right;
}

.avatar-uploader>.ant-upload {
  width: 128px;
  height: 128px;
}

.ant-upload-select-picture-card i {
  font-size: 32px;
  color: #999;
}

.ant-upload-select-picture-card .ant-upload-text {
  margin-top: 8px;
  color: #666;
}
</style>
