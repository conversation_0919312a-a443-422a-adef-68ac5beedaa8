import request from '@/utils/request'
import qs from 'qs'
const userApi = {
  agentcashlist: '/agent/cash/list.do', // 资金记录
  agentrechargelist: '/agent/recharge/list.do', // 充值记录
  agentwithdrawlist: '/agent/withdraw/list.do', // 出金记录
  rechargeList: '/agent/recharge/list.do',  // 充值列表
  rechargeDel: '/agent/recharge/del.do', // 充值列表删除
  rechargeUpdateState: '/agent/recharge/updateState.do', // 充值列表修改状态
  rechargeCreateOrder: '/agent/recharge/createOrder.do', // 新增充值订单
  rechargeExport: '/agent/recharge/export.do', // 充值订单导出
  withdrawList: '/agent/withdraw/list.do', // 提现列表
  withdrawUpdateState: 'agent/withdraw/updateState.do', // 提现列表修改状态
  withdrawCheck: '/agent/withdraw/check.do', // 提现审核
  withdrawExport: '/agent/withdraw/export.do', // 提现列表导出
  cashList: '/agent/amountChange/list', // 资金记录
  getAmountChangeType: '/agent/amountChange/getAmountChangeType', // 账变类型
}

export function agentcashlist(parameter) {
  return request({
    url: userApi.agentcashlist,
    method: 'post',
    data: qs.stringify(parameter),
  })
}

export function agentrechargelist(parameter) {
  return request({
    url: userApi.agentrechargelist,
    method: 'post',
    data: qs.stringify(parameter),
  })
}

export function agentwithdrawlist(parameter) {
  return request({
    url: userApi.agentwithdrawlist,
    method: 'post',
    data: qs.stringify(parameter),
  })
}


export function rechargelist(parameter) {
  return request({
    url: userApi.rechargeList,
    method: 'post',
    data: qs.stringify(parameter),
  })
}

export function rechargedel(parameter) {
  return request({
    url: userApi.rechargeDel,
    method: 'post',
    data: qs.stringify(parameter),
  })
}

export function rechargeupdateState(parameter) {
  return request({
    url: userApi.rechargeUpdateState,
    method: 'post',
    data: qs.stringify(parameter),
  })
}

export function rechargecreateOrder(parameter) {
  return request({
    url: userApi.rechargeCreateOrder,
    method: 'post',
    data: qs.stringify(parameter),
  })
}

export function rechargeexport(parameter) {
  return request({
    url: userApi.rechargeExport,
    method: 'post',
    responseType: 'blob',
    data: qs.stringify(parameter),
  })
}

export function withdrawlist(parameter) {
  return request({
    url: userApi.withdrawList,
    method: 'post',
    data: qs.stringify(parameter),
  })
}

export function withdrawupdateState(parameter) {
  return request({
    url: userApi.withdrawUpdateState,
    method: 'post',
    data: qs.stringify(parameter),
  })
}

export function withdrawcheck(parameter) {
  return request({
    url: userApi.withdrawCheck,
    method: 'post',
    data: qs.stringify(parameter),
  })
}

export function withdrawexport(parameter) {
  return request({
    url: userApi.withdrawExport,
    method: 'post',
    responseType: 'blob',
    data: qs.stringify(parameter),
  })
}

export function cashlist(parameter) {
  return request({
    url: userApi.cashList,
    method: 'get',
    params: parameter,
  })
}


export function getAmountChangeType(parameter) {
  return request({
    url: userApi.getAmountChangeType,
    method: 'post',
    data: qs.stringify(parameter || {})
  })
}

