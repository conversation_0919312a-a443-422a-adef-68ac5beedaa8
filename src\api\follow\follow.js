import request from '@/utils/request'

/**
 * 分页查询跟单列表
 * @param parameter 请求参数
 * @returns {*}
 */
export function pageList(parameter) {
  return request({
    url: '/agent/follow/list.do',
    method: 'get',
    params: parameter
  })
}

/**
 * 获取跟单详情
 * @param parameter
 * @returns {*}
 */
export function getDetail(parameter) {
  return request({
    url: '/agent/follow/detail.do',
    method: 'post',
    data: parameter
  })
}

/**
 * 创建跟单申请
 * @param parameter
 * @returns {*}
 */
export function create(parameter) {
  return request({
    url: '/agent/follow/create.do',
    method: 'post',
    data: parameter
  })
}

/**
 * 审核跟单申请
 * @param parameter
 * @returns {*}
 */
export function audit(parameter) {
  return request({
    url: '/agent/follow/audit.do',
    method: 'post',
    headers: {
      'Content-Type': 'application/json' // Added Content-Type header
    },
    data: parameter
  })
}

/**
 * 审核追加申请
 * @param parameter
 * @returns {*}
 */
export function auditAdd(parameter) {
  return request({
    url: '/agent/follow/audit-add.do',
    method: 'post',
    headers: {
      'Content-Type': 'application/json' // Added Content-Type header
    },
    data: parameter
  })
}

/**
 * 获取跟单列表
 * @param parameter 请求参数
 * @returns {*}
 */
export function getFollowList(parameter) {
  return request({
    url: '/agent/product/follow/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加跟单
 * @param parameter 请求参数
 * @returns {*}
 */
export function addFollow(parameter) {
  return request({
    url: '/agent/product/follow/add',
    method: 'post',
    headers: {
      'Content-Type': 'application/json' // Added Content-Type header
    },
    data: parameter
  })
}

/**
 * 修改跟单
 * @param parameter 请求参数
 * @returns {*}
 */
export function updateFollow(parameter) {
  return request({
    url: '/agent/product/follow/update',
    method: 'post',
    headers: {
      'Content-Type': 'application/json' // Added Content-Type header
    },
    data: parameter
  })
}

/**
 * 删除跟单
 * @param parameter 请求参数
 * @returns {*}
 */
export function deleteFollow(parameter) {
  return request({
    url: '/agent/product/follow/delete',
    method: 'post',
    params: parameter
  })
}

/**
 * 更新跟单状态
 * @param parameter 请求参数
 * @returns {*}
 */
export function updateFollowStatus(parameter) {
  return request({
    url: '/agent/product/follow/status',
    method: 'post',
    data: parameter
  })
}

/**
 * 获取待审核追加数量
 * @returns {*}
 */
export function getPendingAddCount() {
  return request({
    url: '/agent/follow/pending-add-count.do', // 假设的接口路径
    method: 'get'
  })
}

/**
 * 修改跟单仓位
 * @param parameter {id: number, commissionRate: number}
 * @returns {*}
 */
export function updatePosition(parameter) {
  return request({
    url: '/agent/follow/update.do', // Corrected endpoint
    method: 'post',
    headers: {
      'Content-Type': 'application/json' // Added Content-Type header
    },
    data: parameter
  })
}

/**
 * 查询追加审核详情
 * @param parameter {id: number}
 * @returns {*}
 */
export function queryAppendDetail(parameter) {
  return request({
    url: '/agent/follow/queryAppendDetail.do',
    method: 'post',
    headers: {
      'Content-Type': 'application/json' // Added Content-Type header
    },
    data: parameter
  })
}